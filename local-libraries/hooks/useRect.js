import { useLayoutEffect, useCallback, useState } from "react";

export default function useRect(ref) {
  const [rect, setRect] = useState(() => getRect(ref?.current));

  const handleResize = useCallback(() => {
    const currentRef = ref.current;
    if (currentRef) {
      setRect(getRect(currentRef));
    }
  }, [ref]);

  useLayoutEffect(() => {
    const element = ref.current;
    if (!element) return;

    handleResize();

    const resizeObserver =
      typeof ResizeObserver === "function"
        ? new ResizeObserver(handleResize)
        : null;

    if (resizeObserver) {
      resizeObserver.observe(element);
      return () => resizeObserver.disconnect();
    } else {
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
    }
  }, [ref]);

  return rect;
}

function getRect(element) {
  return element
    ? element.getBoundingClientRect()
    : {
        bottom: 0,
        height: 0,
        left: 0,
        right: 0,
        top: 0,
        width: 0,
      };
}
