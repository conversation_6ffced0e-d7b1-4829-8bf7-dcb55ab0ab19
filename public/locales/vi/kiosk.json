{"nhapMaNguoiBenhHoacQuetQRCode": "<PERSON><PERSON><PERSON><PERSON> \"Mã người bệnh\" hoặc Quét QR Code (đã có ở lần khám trước)", "nhapHoacQuetQRDeTimMaNguoiBenh": "<PERSON><PERSON>ậ<PERSON> hoặc quét QR để tìm mã người bệnh", "phongQuayThucHien": "Phòng/quầy thực hiện", "hienThiStt": "<PERSON><PERSON><PERSON> thị STT", "hienThiOQuetMa": "<PERSON><PERSON><PERSON> thị ô quét mã", "kioskDangKyKhamBenhTuDong": "KIOSK Đăng ký khám bệnh tự động", "xinKinhChaoQuyKhach": "<PERSON>n k<PERSON>h chào Quý khách!", "vuiLongChonKhuVucKhamSauDayDeDangKyKham": "<PERSON>ui lòng chọn khu vực khám sau đây để đăng ký khám!", "nguoiBenhLaDoiTuongUuTien": "<PERSON><PERSON><PERSON><PERSON> bệnh là đối tượng ưu tiên", "dangXuat": "<PERSON><PERSON><PERSON> xu<PERSON>", "quayLai": "Quay lại", "ngayThangNamSinh": "<PERSON><PERSON><PERSON> th<PERSON>g n<PERSON>m sinh", "viDu": "VD", "luuVaLaySo": "<PERSON><PERSON><PERSON> và lấy số", "hayChoDenSoThuTuCuaMinhDeDuocPhucVu": "<PERSON><PERSON><PERSON> chờ đến số thứ tự của mình để đư<PERSON><PERSON> phụ<PERSON> vụ.", "laySoThuTuThanhCong": "<PERSON><PERSON><PERSON> số thứ tự thành công", "laDoiTuongUuTien": "<PERSON><PERSON> đối tượng ưu tiên", "soThuTuCuaBanLa": "<PERSON><PERSON> thứ tự của bạn là", "xinMoiLayPhieuSTTInRa": "<PERSON>n mời lấy phiếu STT in ra", "laySo": "LẤY SỐ", "xinMoiDatheBHYTCCCDTruocCameraCuaThietBiDeQuetQRCode": "<PERSON>n mời đặt thẻ BHYT, CCCD trước camera củ<PERSON> thiết bị để Quét QR Code!", "kioskTiepDonKhamBenhTuDong": "<PERSON><PERSON><PERSON> tiế<PERSON> đ<PERSON> khám bệnh tự động", "vuiLongQuetMaDinhDanh": "<PERSON><PERSON> lòng quét mã định danh", "cungCapThongTinSau": "<PERSON><PERSON> cấp thông tin sau", "maTheBYHTHoacCCCD": "Mã thẻ BHYT hoặc CCCD", "maQR": "Mã QR", "hoac": "Hoặc", "dangKyKhamBenh": "ĐĂNG KÝ KHÁM BỆNH", "vuiLongXacNhanThongTin": "<PERSON><PERSON> lòng xác nhận thông tin", "khongCoThongTinCuaToi": "<PERSON>hông có thông tin của tôi", "toiMuonDangKyMoi": "<PERSON><PERSON><PERSON> muốn đăng ký mới", "choTiepDon": "CHỜ TIẾP ĐÓN", "nb": "NB", "dangTiepDon": "ĐANG TIẾP ĐÓN", "khamBHYT": "Khám BHYT", "khongBHYT": "Không BHYT", "vuiLongChonKhuVuc": "Vui lòng chọn khu vực!", "dangKyKhamBenhBangThongTinCaNhan": "ĐĂNG KÝ KHÁM BỆNH BẰNG THÔNG TIN CÁ NHÂN", "lanDauDenKham": "<PERSON><PERSON><PERSON> đầu đến khám", "chungMinhThuNhanDan": "<PERSON><PERSON><PERSON> minh thư nhân dân", "maQRNb_CCCD_RFID": "Mã QR ngư<PERSON>i b<PERSON>nh, CCCD, RFID,...", "luuY_NenNhap": "<PERSON><PERSON>u ý: <PERSON><PERSON><PERSON>p", "daSuDungTrongCacLanKhamTruoc": "đã sử dụng trong các lần khám trước", "soCmndSaiDinhDang": "Số CMND sai định dạng!", "chinhSuaThongTinCaNhan": "CHỈNH SỬA THÔNG TIN CÁ NHÂN", "moiNhapHoVaTen": "<PERSON><PERSON><PERSON> nhập họ và tên!", "vuiLongNhapNgayThangNamSinh": "<PERSON>ui lòng nhập ngày tháng năm sinh!", "soNhaThonXom": "Số nhà / Thôn / Xóm", "nhapSoNha": "<PERSON><PERSON><PERSON><PERSON> số nhà", "vdSoNhaThonXom": "VD: S<PERSON> 8 Tổ 28", "phuongXaTinhThanhPho": "Phường / Xã, Tỉnh / Thành phố", "vuiLongNhapDiaChi": "<PERSON><PERSON> lòng nhập địa chỉ!", "vdDiaChi": "VD: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>", "treEmDuoi6Tuoi": "Trẻ em dưới 6 tuổi", "nguoiKhuyetTatNang": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ết tật nặng", "nguoiTuDu75TuoiTroLen": "Người từ đủ 75 tuổi trở lên", "nguoiCoCongVoiCachMang": "Ngườ<PERSON> có công với cách mạng", "phuNuCoThai": "<PERSON><PERSON> nữ có thai", "xinVuiLongNhapMaHoSoHoacDatPhieuKham": "<PERSON>n vui lòng nhập mã hồ sơ hoặc đặt phiếu khám/chỉ định trước vùng quét của thiết bị để quét QR Code", "soThuTuCuaBan": "SỐ THỨ TỰ CỦA BẠN", "neuThongThayPhieuSttInRa": "<PERSON><PERSON><PERSON> không thấy phiếu STT in ra, vui lòng liên hệ nhân viên bệnh viện!", "vuiLongChonPhongQuayCanThietLap": "<PERSON><PERSON> lòng chọn phòng/qu<PERSON><PERSON> cần thiết lập", "vuiLongQuetMaHs": "<PERSON>ui lòng quét mã HS", "kioskLaySoTiepDon": "<PERSON><PERSON><PERSON> lấy số tiếp đón", "kioskLaySoThuNgan": "<PERSON><PERSON><PERSON> l<PERSON>y số thu ngân", "kioskTiepDonDienThongTinNB": "Kiosk tiếp đón điền thông tin NB", "kioskQuetMaQR": "Kiosk quét mã QR", "kioskQuetVaGoiNBVaoQuay": "Kiosk quét và gọi NB vào quầy", "vuiLongChonQuay": "Vui lòng chọn quầy!", "chonPhongQuay": "Chọn phòng/quầy", "maHoSoF1": "<PERSON><PERSON> hồ sơ [F1]", "maHoSoF2": "<PERSON><PERSON> hồ sơ [F2]", "thieuThongTinNb": "<PERSON><PERSON><PERSON><PERSON> thông tin ngư<PERSON>i b<PERSON>nh", "khongTonTaiMaHoSo": "<PERSON><PERSON><PERSON><PERSON> tồn tại mã hồ sơ", "khongTonTaiMaNb": "Không tồn tại mã NB", "trongDanhSachCho": "trong danh sách chờ!", "coLoiXayRaVuiLongThuLai": "<PERSON><PERSON> lỗi xảy ra xin vui lòng thử lại", "vuiLongQuyetMaHsMaNb": "<PERSON><PERSON> lòng quét mã HS, mã NB", "CMND/CCCD": "CMND/CCCD", "quetMaNguoiBenh": "<PERSON><PERSON><PERSON> mã ng<PERSON><PERSON><PERSON> b<PERSON>nh", "khongTonTaiMaHoSo{{maHoSo}}": "<PERSON><PERSON><PERSON><PERSON> tồn tại mã hồ sơ {{maHoSo}}", "khongTonTaiMaThe{{maThe}}": "<PERSON><PERSON><PERSON><PERSON> tồn tại mã thẻ {{maThe}}", "duLieuNhapSaiDinhDang": "<PERSON><PERSON> liệu nhập sai định dạng", "hienThiOQuetMaHienThiNbLenDsCho": "Hiển thị ô quét mã hiển thị NB lên DS chờ", "hienThiOQuetGoiNbVaoPhongQuay": "Hiển thị ô quét gọi NB vào phòng/quầy", "nhanDienKhuonMat": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>n khuôn mặt", "khongTimThayTheDuocDatOMayQuet": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thẻ được đặt ở máy quét", "vuiLongDungThangVaDieuChinhKhuonMatVaoGiuaKhungHinh": "<PERSON><PERSON> lòng đứng thẳng và điều chỉnh khuôn mặt vào giữa khung hình", "khongTrungKhopThongTin": "Không trùng khớp thông tin xác thực. Vui lòng chụp lại ảnh hoặc ấn bỏ qua để lấy STT", "tinh/thanhPho": "Tỉnh/Thành phố", "chonTinhThanhPho": "<PERSON>ọn tỉnh thành phố", "quan/huyen": "Quận/Huyện", "chonQuanHuyen": "<PERSON><PERSON><PERSON> quận huy<PERSON>n", "xa/phuong": "Xã/phường", "chonPhuongXa": "<PERSON>ọn phường xã", "diaChiThuongTru": "Địa chỉ thường trú", "laySoThuTu": "<PERSON><PERSON><PERSON> số thứ tự", "nhapDiaChiThuongTru": "<PERSON><PERSON><PERSON><PERSON> địa chỉ thường trú", "dangXuLyDuLieu": "<PERSON><PERSON> xử lý dữ liệu. <PERSON>ui lòng chờ trong giây lát!", "soDienThoai": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "maTheQrTheBHYTCCCD": "Mã QR thẻ BHYT, CCCD", "phuongThucXacThucKhuonMat": "<PERSON><PERSON><PERSON><PERSON> thức xác thực khu<PERSON>n mặt", "nguoiDungTuThaoTacNhanChup": "Người dùng tự thao tác nhấn chụp", "heThongTuChupHinh": "Hệ thống tự động chụp hình", "dangXacThucKhuonMat": "<PERSON><PERSON> xác thực khuôn mặt. <PERSON><PERSON> lòng nhìn thẳng mặt vào camera", "vuiLongTichDongYVoiDieuKhoanBaoMat": "Vui lòng tích đồng ý với điều khoản bảo mật", "vuiLongNhapHoVaTen": "Vui lòng nhập họ và tên!", "dangPhanTichDuLieuKhuonMat": "<PERSON><PERSON> phân tích dữ liệu khuôn mặt", "vuiLongNguoiBenhDeTheCccdVaoMayQuet": "<PERSON>ui lòng người bệnh để thẻ CCCD vào má<PERSON> quét", "khongTimThayTheOViTriMayQuet": "<PERSON><PERSON><PERSON>ng tìm thấy thẻ ở vị trí máy quét. Vui lòng bấm xác nhận và đặt lại thẻ vào máy quét", "thietLapChonKhuVuc": "<PERSON><PERSON><PERSON><PERSON> lập chọn khu vực", "thietLapKiosk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chonThietLap": "<PERSON><PERSON><PERSON> thi<PERSON> lập", "kioskLaySttTheoLoaiDoiTuong": "<PERSON>osk lấy số thứ tự theo loại đối tượng", "vuiLongDoiTuongNguoiBenhSauDayDeDangKyKhamBenh": "<PERSON>ui lòng chọn đối tượng người bệnh sau đây để đăng ký khám!", "thietLapChonLoaiDoiTuong": "<PERSON><PERSON><PERSON><PERSON> lập chọn loại đối tượng", "nhapMaNguoiBenhHoacQuetQRCode2": "<PERSON><PERSON><PERSON><PERSON> \"Mã người bệnh\" hoặc Quét QR Code", "hienThiTongSoDaLay": "<PERSON><PERSON><PERSON> thị tổng số đã lấy", "hienIconBanTay": "Hiển thị icon b<PERSON><PERSON> tay", "khongTonTaiKhuVucCoStt": "<PERSON><PERSON><PERSON><PERSON> tồn tại khu vực có số thứ tự là {{stt}}", "khongTonTaiLoaiDoiTuongCoStt": "<PERSON><PERSON><PERSON><PERSON> tồn tại loại đối tượng có số thứ tự là {{stt}}", "kioskThanhToanTuDong": "<PERSON><PERSON><PERSON>h toán tự động", "thanhToanTuDong": "<PERSON><PERSON> toán tự động", "vuiLongQuetMaQrCodeTrenCccdAppVneidHoacMaQrCodeDaDuocCap": "<PERSON>ui lòng quét mã QR code trên CCCD, ứng dụng VNEID hoặc mã QR code đã được cấp trong quá trình thăm khám", "qrCccdQrMaNb": "QR CCCD, QR mã NB", "nguoiBenhConThieuTamUngVuiLongQuetMaQrTrenPhieuDeNapTienVaTiepTucThanhToan": "<PERSON><PERSON><PERSON><PERSON> bệnh còn thiếu tạm ứng. <PERSON><PERSON> lòng quét mã QR trên phiếu để nạp thêm tiền và tiếp tục thanh toán", "thanhToanTuDongThanhCongVuiLongNhanSoThuTuLayThuocBHYT": "<PERSON>h toán tự động thành công. <PERSON><PERSON> lòng nhận số thứ tự lấy thuốc BHYT.", "thanhToanTuDongThanhCong": "<PERSON><PERSON> toán tự động thành công", "xinMoiLayPhieuDaDuocNhaRa": "<PERSON>n mời lấy phiếu đã được nhả ra", "thanhToanTuDongKhongThanhCongMessage": "<b><PERSON><PERSON> toán tự động không thành công. THÔNG BÁO</b>: <span>\"{{message}}\".</span>", "dangXuLyVuiLongCho": "<PERSON><PERSON> xử lý. <PERSON><PERSON> lòng chờ...", "khongTimThayThongTinNguoiBenh": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin người bệnh. <PERSON>ui lòng thử lại!", "kioskTiepDonNguoiBenh": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> đ<PERSON> ng<PERSON><PERSON> b<PERSON>nh", "kioskDangKyKhamMoiTaiKham": "Kiosk đăng ký khám mới và tái khám", "laySoThuTuTiepDon": "<PERSON><PERSON><PERSON> số thứ tự tiếp đón", "vuiLongQuetMaQrCodeTrenCccdAppVneidAppBenhVienHoacMaQrCodeDaDuocCap": "<PERSON><PERSON> lòng quét mã QR trên CCCD, <PERSON>ng dụng VNeID, <PERSON>ng dụng Bệnh viện hoặc mã QR in trên giấy hẹn, đơn thuốc để đăng ký khám và tái khám", "dangKy": "<PERSON><PERSON><PERSON> ký", "soDienThoaiKhongHopLe": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ", "nhapSoGiayToTuyThan": "<PERSON><PERSON><PERSON><PERSON> số giấy tờ tùy thân", "dangKyTaiKhamThanhCongVuiLongNhanSoThuTuVaLenPhongKham": "<PERSON><PERSON><PERSON> ký tái khám thành công. <PERSON><PERSON> lòng nhận số thứ tự và lên phòng khám.", "thanhCong": "<PERSON><PERSON><PERSON><PERSON> công", "inSoThuTuTuDong": "In số thứ tự tự động", "quetMaLaySoThuTuThucHienDichVu": "<PERSON><PERSON><PERSON> mã l<PERSON>y số thứ tự thực hiện dịch vụ", "kioskPhanPhongThucHien": "Kiosk phân phòng thực hiện", "vuiLongNhapSoGiayToTuyThan": "<PERSON><PERSON> lòng nhập số giấy tờ tùy thân!"}