{"thietLapThongSo": "SETTING PARAMETERS", "tenTruongTrenMhKiosk": "Field name on Kiosk screen", "hienThi": "Display", "tenThietBi": "Device name", "bacSi": "Doctor", "yTa": "Nurse", "hoTro": "Support", "diaChiMac": "MAC address", "quayTiepDon": "Reception desk", "quayThuNgan": "Cashier", "thoiGianLamViec": "Working time", "mauQms": "QMS template", "datLai": "Reset", "phongQuay": "Room/Counter", "DANG_TIEP_DON": "WELCOMING", "DANG_KHAM": "ONGOING EXAMINATION", "CHO_TIEP_DON": "WAITING FOR RECEPTION", "CHO_KHAM": "WAITING FOR EXAMINATION", "heThongXepHangCho": "QUEUE MANAGEMENT SYSTEM", "xinKinhChaoQuyKhach": "Welcome!", "thietLap": "Settings", "chonLoaiQms": "Select QMS type", "chonMotThietLapThongSoCoSanDuoiDay": "Select one of the preset configurations below or", "nguoiBenhGoiNho": "MISSING CALLS FROM PATIENTS", "dSNBCoKQCLS": "List of patients with paraclinical test results", "dangTiepDon": "ACCEPTING", "dangKham": "Ongoing examination", "choTiepDon": "Waiting for reception", "choKham": "Waiting for examination", "khongTheCheckinKhiThietLap": "Cannot check-in while multiple rooms are being displayed", "checkInDuLieuSuccess": "Successfully Checked-in data!", "khongLamViec": "Not working", "dangLamViec": "Working", "troLyYta": "Assistant - Nurse", "hoTroHuongDan": "Support - Guide", "dangKhamBr": "UNDERGOING <br /> EXAMINATION", "tiepTheoBr": "NEXT <br /> ", "dangThucHienBr": "IN <br /> PROGRESS", "qrCodeDescription": "Please place the note containing QR code into the scanning area below for the system to verify.", "quetMaQr": "Scan QR code", "daXacNhan": "CONFIRMED", "choXacNhan": "WAITING FOR CONFIRMATION", "benhNhanGoiNho": "MISSING CALLS FROM PATIENTS", "vuiLongChonThoiGianLamViec": "Please select working hours!", "vuiLongNhapTenThietBi": "Please enter device name!", "nhapTenThietBi": "Enter the device name", "vuiLongChonKhoa": "Please select the department!", "chonKhoa": "Select department", "vuiLongChonQuayTiepDon": "Please select reception counter!", "vuiLongChonQuayThuNgan": "Please select cashier counter!", "chonQuayTiepDon": "Select reception counter!", "chonQuayThuNgan": "Select cashier counter!", "vuiLongChonMaMau": "Please select template code!", "vuiLongChonTemplate": "Please select template", "chonTemplate": "Select emplate", "luaChonPhongKhamVaBacSi": "Select Clinic and Doctor", "nhapDiaChiMac": "Enter Mac address", "vuiLongChonYta": "Please select nurse assistant!", "chonTroLyYta": "Select nurse assistant", "vuiLongChonHoTroHd": "Please select support - guide!", "chonHoTroHd": "Select support - guide", "sangTu": "Morning from", "den": "To", "chieuTu": "Afternoon from", "loaiQms": "QMS type", "tongTienGiam": "Total Money reduced", "tongTienDichVu": "Service discount", "%giamDichVu": "% reduction in service", "tenTruongTrenMh": "School name on MH", "diaChiMacThietBi": "<PERSON>ce Mac address", "nhapDiaChiMacThietBi": "Enter device mac address", "vuiLongChonLoaiQms": "Please select qms type!", "videoGioiThieu": "Introductory video", "chonHoTroHuongDan": "Select tutorial support", "loaiDanhSach": "List type", "vuiLongChonLoaiDanhSach": "Please select a listing type", "chonLoaiDanhSach": "Choose the list type", "choPhauThuat": "Waiting for surgery", "choPt": "Waiting for Surgery", "dangPhauThuat": "Having surgery", "dangPt": " In Surgery", "xongPhauThuat": "Done with surgery", "xongPt": "Surgery Completed", "vuiLongChonKhuVuc": "Please select area", "daChuyenPhongHoiTinh": "Moved to recovery room", "batDau": "<PERSON><PERSON>", "danhSachLichPhauThuat": "List of surgical schedules", "khoaXuat": "Export Department", "nhapMaQRCode": "Enter QR Code", "maMau": "Sample code", "video": "Video", "vuiLongThucHienThanhToanDichVu": "Please make payment for services <br/&gt; before checkin", "xacThucThongTinBangKhuonMat": "Face authentication", "tuyenBoQuyenRiengTu": "Privacy Statement", "noiDungQuyenRiengTu": {"tieuDe1": "Please place your Citizen Identification Card in the scanning position. The information on the Citizen ID card will be used for facial recognition", "tieuDe2": "ISOFH is committed to and respects the privacy of all customers when using facial information. We understand that privacy is an important value and are committed to protecting your personal information in a trustworthy manner. Below is our privacy statement:", "tieuDeChiTiet1": "Explicit Consent", "noiDungChiTiet1": "Any use of your facial information requires your express prior consent. We undertake not to use or share this information without your consent.", "tieuDeChiTiet2": "Compliance with the Law", "noiDungChiTiet2": "Any activities involving the use of your facial information will fully comply with the laws and regulations on privacy and personal data protection.", "tieuDeChiTiet3": "Information Security", "noiDungChiTiet3": "We are committed to implementing robust security measures to ensure patient 's facial information is protected from unauthorized access or misuse.", "tieuDeChiTiet4": "Clear Purpose of Use", "noiDungChiTiet4": "We will only use your facial information for clearly stated and pre-announced purposes, such as user authentication or providing related services.", "tieuDeChiTiet5": "Right to Withdraw and Delete Information", "noiDungChiTiet5": "You have the right to withdraw your consent to use your facial information at any time, and we are committed to deleting or ceasing to use that information upon your request.", "noiDungCuoi": "We believe that protecting our customers' privacy is not only a legal responsibility but also a foundation for building trust and satisfaction from users. Privacy is our core value, and we are committed to maintaining and enhancing this level of protection in all of the company's activities."}, "toiDaDocVaDongYVoiCacDieuKhoan": "I have read and agree to the terms", "khongTheTimthayTheDuocDatOMayQuet": "(Card not found placed in scanner)", "tiepTuc": "Continue", "nhanDienKhuonMat": "Facial recognition", "quayGoiSo": "Call counter", "hienThiLenQMS": "Display on QMS", "goiNho": "Missed call", "goiNhoEng": "MISSED CALL", "dSNBCoKQCLSEng": "List of patients with paraclinical results", "dangTiepDonEng": "RECEPTION IN PROGRESS", "dangKhamEng": "IN CONSULTATION", "choTiepDonEng": "PENDING RECEPTION", "choKetLuan": "WAIT FOR THE CONCLUSION", "choKetLuanEng": "PENDING DIAGNOSIS", "choThucHien": "WAITING TO BE IMPLEMENTED", "choThucHienEng": "PENDING PROCEDURE", "choKhamEng": "PENDING EXAMINATION", "choPhauThuatEng": "PENDING SURGERY", "dangPhauThuatEng": "IN PROGRESS SURGERY", "xongPhauThuatEng": "COMPLETED SURGERY", "danhSachLichPhauThuatEng": "LIST OF SURGERY SCHEDULE", "khoaTitle": "Department {{title}}", "khuVucTitle": "Area {{title}}", "xinMoiNguoiBenhSttVao": "Please invite the patient with ID {{ stt }} to {{ ten }}.", "xinMoiNguoiBenhTenVao": "Please invite the patient {{ tenNb  }} {{ tuoi2 }} to {{ ten }}.", "tongSoDaLay": "Total amount taken", "sttKhongTonTaiTrongNgay": "The order number does not exist on this day!", "sttDaTiepDonKhongTheTiepDonLaiTrongNgay": "STT has welcomed, cannot welcome again on the same day!", "taiISOFHCARE": "Download ISOFHCARE", "datKhamTrucTuyen": "Online appointment booking", "datLichXetNghiemCoVid": "Schedule a Covid test.", "khamQuaVideoCall": "Consultation via video call", "dangThucHien": "CURRENTLY IN PROGRESS", "dangThucHienEng": "IN PROGRESS", "vuiLongChonLoaiHienThi": "Please select a display type!", "vuiLongChonToiDa8Phong": "Please select a maximum of 8 rooms!", "vuiLongThietLapPhongKhamKhac": "Please set up another clinic!", "vuiLongThietLapQuayKhac": "Please set up another counter!", "vuiLongThietLapPhong": "Please set up the room!", "vuiLongThietLapQuay": "Please set up the counter!"}