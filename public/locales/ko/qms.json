{"thietLapThongSo": "매개변수 설정", "tenTruongTrenMhKiosk": "MH 키오스크의 학교 이름", "hienThi": "표시하다", "tenThietBi": "장치 이름", "bacSi": "의사", "yTa": "간호사", "hoTro": "지원하다", "diaChiMac": "MAC 주소", "quayTiepDon": "접수 카운터", "quayThuNgan": "출납원", "thoiGianLamViec": "근무 시간", "mauQms": "QMS 양식", "datLai": "초기화", "phongQuay": "룸/카운터", "DANG_TIEP_DON": "환영", "DANG_KHAM": "검사 중", "CHO_TIEP_DON": "수신을 기다리는 중", "CHO_KHAM": "시험을 기다리는 중", "heThongXepHangCho": "대기줄 시스템", "xinKinhChaoQuyKhach": "인사드립니다", "thietLap": "설립하다", "chonLoaiQms": "QMS 유형 선택", "chonMotThietLapThongSoCoSanDuoiDay": "아래에서 사용 가능한 매개변수 설정을 선택하거나", "nguoiBenhGoiNho": "환자가 전화를 받지 못했습니다", "dSNBCoKQCLS": "CLS 결과가 포함된 NB 목록", "dangTiepDon": "환영", "dangKham": "검사 중", "choTiepDon": "수신을 기다리는 중", "choKham": "시험을 기다리는 중", "khongTheCheckinKhiThietLap": "멀티룸 표시 설정 시 체크인이 불가능합니다.", "checkInDuLieuSuccess": "데이터를 성공적으로 체크인했습니다!", "khongLamViec": "작동 안함", "dangLamViec": "일하고 있는", "troLyYta": "보조자 - 간호사", "hoTroHuongDan": "지원 - 지침", "dangKhamBr": "&lt;br /&gt;시험", "tiepTheoBr": "다음 &lt;br /&gt; 다음", "dangThucHienBr": "&lt;br /&gt;진행 중", "qrCodeDescription": "시스템 확인을 위해 아래 스캔 영역에 QR 코드가 포함된 티켓을 삽입해 주세요.", "quetMaQr": "QR 코드를 스캔하세요", "daXacNhan": "확인됨", "choXacNhan": "확인을 기다리세요", "benhNhanGoiNho": "환자의 부재중 전화", "vuiLongChonThoiGianLamViec": "근무시간을 선택해주세요!", "vuiLongNhapTenThietBi": "기기 이름을 입력해주세요!", "nhapTenThietBi": "장치 이름을 입력하세요", "vuiLongChonKhoa": "학과를 선택해주세요!", "chonKhoa": "교수진 선택", "vuiLongChonQuayTiepDon": "접수 카운터를 선택해주세요!", "vuiLongChonQuayThuNgan": "계산대를 선택해주세요!", "chonQuayTiepDon": "접수처를 선택하세요!", "chonQuayThuNgan": "캐셔를 선택하세요!", "vuiLongChonMaMau": "샘플코드를 선택해주세요!", "vuiLongChonTemplate": "템플릿을 선택하세요", "chonTemplate": "템플릿 선택", "luaChonPhongKhamVaBacSi": "진료소와 의사를 선택하세요", "nhapDiaChiMac": "맥 주소를 입력하세요", "vuiLongChonYta": "간호조무사를 선택해주세요!", "chonTroLyYta": "간호조무사를 선택하세요", "vuiLongChonHoTroHd": "HD 지원을 선택해주세요!", "chonHoTroHd": "HD 지원 선택", "den": "도착하다", "danhSachLichPhauThuat": "수술 일정 목록", "khoaXuat": "수출 부서", "nhapMaQRCode": "QR 코드를 입력하세요", "maMau": "샘플 코드", "video": "동영상", "vuiLongThucHienThanhToanDichVu": "체크인하기 전에 &lt;br/> 서비스 비용을 결제하십시오.", "xacThucThongTinBangKhuonMat": "얼굴로 정보를 인증하세요", "tuyenBoQuyenRiengTu": "개인 정보 보호 정책", "noiDungQuyenRiengTu": {"tieuDe1": "CCCD 카드를 스캔 위치에 놓으십시오. CCCD에 대한 정보는 얼굴 인식 과정에 사용됩니다.", "tieuDe2": "ISOFH는 얼굴 정보를 사용할 때 모든 고객의 개인정보 보호를 약속하고 존중합니다. 우리는 개인 정보 보호가 중요한 가치임을 이해하고 귀하의 개인 정보를 안정적으로 보호하기 위해 최선을 다하고 있습니다. 다음은 당사가 제공하는 개인 정보 보호 정책입니다.", "tieuDeChiTiet1": "명시적 동의", "noiDungChiTiet1": "귀하의 얼굴 정보를 사용하려면 사전에 귀하의 명시적인 승인이 필요합니다. 우리는 귀하의 동의 없이 이 정보를 사용하거나 공유하지 않을 것을 약속합니다.", "tieuDeChiTiet2": "법률 준수", "noiDungChiTiet2": "귀하의 얼굴 정보 사용과 관련된 모든 활동은 개인 정보 보호 및 개인 데이터 보호에 관한 법적 규정 및 법률을 완전히 준수합니다.", "tieuDeChiTiet3": "정보 보안", "noiDungChiTiet3": "우리는 NB의 얼굴 정보가 무단 접근이나 오용으로부터 보호되도록 강력한 보안 조치를 구현하기 위해 최선을 다하고 있습니다.", "tieuDeChiTiet4": "명확한 사용 목적", "noiDungChiTiet4": "당사는 사용자 인증이나 관련 서비스 제공 등 명확하게 명시되고 정보에 입각한 목적으로만 귀하의 얼굴 정보를 사용합니다.", "tieuDeChiTiet5": "정보 철회 및 삭제 권리", "noiDungChiTiet5": "귀하는 언제든지 얼굴 정보 사용에 대한 동의를 철회할 권리가 있으며, 당사는 귀하의 요청에 따라 해당 정보를 삭제하거나 사용을 중단할 것을 약속합니다."}, "toiDaDocVaDongYVoiCacDieuKhoan": "약관을 읽었으며 이에 동의합니다.", "khongTheTimthayTheDuocDatOMayQuet": "(스캐너에 놓인 카드를 찾을 수 없습니다)", "tiepTuc": "계속하다", "nhanDienKhuonMat": "얼굴 인식", "quayGoiSo": "카운터 호출", "hienThiLenQMS": "QMS에 표시됨"}