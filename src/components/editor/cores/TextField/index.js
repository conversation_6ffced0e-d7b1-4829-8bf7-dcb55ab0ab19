import React, {
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperative<PERSON>andle,
  useContext,
  useMemo,
} from "react";
import T from "prop-types";
import TextEdit from "components/editor/cores/TextEdit";
import {
  ContentEditable,
  EMR2Context,
  MultipleLine,
  useEditor,
} from "components/editor/config";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { getValueForm, MODE } from "utils/editor-utils";
import { EMRContext } from "components/editor/config";
import { isArray } from "lodash";
import { useLocation, useParams } from "react-router-dom";

const TextField = forwardRef((props, ref) => {
  const { maBaoCao } = useParams();
  const [state, _setState] = useState({
    disable: false,
    width: 0,
    labelWidth: 0,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const context = useContext(EMRContext);
  const {
    component: { init },
  } = useDispatch();
  const { editorId } = useContext(EMR2Context);
  const signStatus = useEditor(editorId, "signStatus", {});
  const location = useLocation().pathname;
  const headless = location.indexOf("headless-editor") != -1;
  const {
    mode,
    component,
    form,
    formChange,
    focusing,
    other,
    textTransform,
    blockWidth,
    valueEMR,
  } = props;
  const labelRef = useRef(null);
  const refMultipleLine = useRef(null);
  const label = mode === MODE.config ? "label" : "";
  const itemProps = component.props || {};

  const getValue = (id) => {
    const elm = document.getElementById(id);
    return elm ? elm.innerHTML : "";
  };
  useImperativeHandle(ref, () => ({
    collectLabel: () => getValue(`${component.type}_${component.key}`),
  }));

  useEffect(() => {
    if (labelRef.current) {
      setState({
        width: blockWidth - labelRef.current.node.clientWidth - 6,
        labelWidth: labelRef.current.node.clientWidth,
      });
    }
  }, [state.labelValue, blockWidth]);

  useEffect(() => {
    setState({
      labelValue: itemProps.label,
    });
  }, [component]);

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    } else {
      if (refMultipleLine.current) refMultipleLine.current.focus(false);
    }
  };

  useEffect(() => {
    const isDisable = context.isDisable;
    if (itemProps.disabled) {
      itemProps.disabled = false;
    }
    let disable = isDisable({ itemProps, signStatus, props });

    if (state.disable != disable) {
      setState({
        disable,
      });
    }
  }, [signStatus, itemProps, props.disable]);

  const lineHeightText =
    (((itemProps.fontSize || props.fontSize || 12) * 4) / 3) *
    (itemProps.lineHeight || props.lineHeightText);

  let minHeight = itemProps.line
    ? itemProps.line * lineHeightText
    : lineHeightText;

  let value = useMemo(() => {
    const value = getValueForm(form, itemProps.fieldName);
    const valueDefault = getValueForm(form, itemProps.fieldName2);
    // if (itemProps.fieldName == "duLieu2[0]_nhanDinh")
    // {
    //   getValueForm(form, itemProps.fieldName);
    // }
    if (maBaoCao === "EMR_BA098" && itemProps.fieldName === "soBaoHiemXaHoi") {
      if (
        !value &&
        form.maTheBhyt &&
        typeof form.maTheBhyt === "string" &&
        form.maTheBhyt?.length >= 10
      ) {
        const _value = form.maTheBhyt.slice(-10);
        if (formChange[itemProps.fieldName]) {
          formChange[itemProps.fieldName](_value);
        }

        return _value;
      }
    }
    if (itemProps.inputNumber) {
      if (value == 0) {
        return 0;
      }
      if (!value && valueDefault == 0) {
        return 0;
      }
    }
    return value || valueDefault || "";
  }, [JSON.stringify(form)]);

  const handleOnChange = (e) => {
    if (state.disable) return; // chặn khi khoá biểu mẫu sau khi ký vẫn chạy onChange
    if (formChange[itemProps.fieldName]) {
      if (itemProps.isDataArray) {
        // check xem giá trị đầu vào có là mảng hay không nếu là mảng khi đẩy lên sẽ conver thành mảng
        const valueSplit = e.htmlValue
          .replaceAll("</div>", "")
          .split("<br>")
          .filter((item) => item);
        formChange[itemProps.fieldName](valueSplit);
      } else if (itemProps.inputNumber) {
        let value = e.htmlValue;
        if (itemProps.heSoQuyDoi) {
          value = +value / itemProps.heSoQuyDoi;
        }
        formChange[itemProps.fieldName](value);
      } else {
        let value = e.htmlValue;
        if (itemProps.heSoQuyDoi) {
          value = +value / itemProps.heSoQuyDoi;
        }
        formChange[itemProps.fieldName](
          value || (itemProps?.defaultValue ? "&nbsp;" : null)
        );
      }
    }
  };

  if (value.replaceAll && !itemProps.plainText) {
    value = value.replaceAll("\n", "<br/>");
  }
  let defaultValue = itemProps.defaultValue || "";
  if (defaultValue.replaceAll && !itemProps.plainText) {
    defaultValue = defaultValue.replaceAll("\n", "<br/>");
  }

  const handleShowModal = () => { };

  useEffect(() => {
    if (itemProps.isDataArray && (isArray(value) || !value)) {
      setState({
        value: (value || []).map((item) => `<span>${item}</span>`).join("<br>"),
      });
    } else {
      setState({
        value: value,
      });
    }
  }, [value]);

  const renderPrice = (price) => {
    try {
      if (itemProps.inputNumber && itemProps.typeNumber == "int") {
        return (price || 0).toFixed().formatPrice();
      } else {
        return price.formatPrice();
      }
    } catch (error) {
      return price;
    }
  };

  useEffect(() => {
    if (
      valueEMR &&
      !valueEMR[itemProps.fieldName] &&
      itemProps.autoSaveDefaultValue &&
      formChange[itemProps.fieldName]
    ) {
      setTimeout(() => {
        formChange[itemProps.fieldName](itemProps?.defaultValue);
      }, 2000);
    }
  }, [valueEMR, itemProps.fieldName, formChange]);

  const renderValue = useMemo(() => {
    if (state.value === undefined || state.value === null) {
      if (itemProps.formatPrice) {
        return renderPrice(defaultValue);
      } else {
        return "";
      }
    } else {
      if (itemProps.formatPrice) {
        return renderPrice(state.value);
      } else if (itemProps.heSoQuyDoi) {
        return +state.value * itemProps.heSoQuyDoi;
      } else {
        return state.value + "";
      }
    }
  }, [state.value, defaultValue]);

  const showMarkSpanRow =
    itemProps.markSpanRow === undefined ? true : itemProps.markSpanRow;
  const isHideLineMark =
    (state.disable || itemProps.readOnly) && !showMarkSpanRow;

  return (
    <Main
      onClick={handleFocus}
      focusing={focusing}
      hadLabel={!!itemProps.label}
      data-type="text-field"
      showMarkSpanRow={showMarkSpanRow}
      lineHeight={lineHeightText || 24}
      minHeight={minHeight || 24}
      fontSize={itemProps.fontSize || props.fontSize} //parrent font size
      contentAlign={itemProps.contentAlign || "left"}
      mode={mode}
      disabled={state.disable || itemProps.readOnly}
      border={itemProps.border}
      itemProps={itemProps}
      fontWeight={itemProps.fontWeight ? itemProps.fontWeight : undefined}
      hidePrint={itemProps.hidePrint}
      className={itemProps.hidePrint ? "hidden-element" : ""}
    >
      {mode === MODE.editing ? (
        <MultipleLine
          label={!itemProps.noLabel ? itemProps.label : ""}
          labelWidth={itemProps.labelWidth}
          contentAlign={itemProps.contentAlign}
          contentColor={itemProps.contentColor}
          onChange={handleOnChange}
          value={renderValue}
          extentLine={itemProps.line - 1 < 0 ? 0 : itemProps.line - 1}
          disabled={!!state.disable || itemProps.formatPrice || headless}
          width={blockWidth}
          min={itemProps.line}
          size={itemProps.size || 1000000}
          ref={refMultipleLine}
          lineHeightText={itemProps.lineHeight || props.lineHeightText || 1.5}
          showMarkSpanRow={itemProps.markSpanRow}
          fontSize={itemProps.fontSize || props.fontSize || 12} //parrent font size
          inputNumber={itemProps.inputNumber}
          maxValue={itemProps.maxValue}
          minValue={itemProps.minValue}
          typeNumber={itemProps.typeNumber}
          onClick={handleShowModal}
          toUpperCaseText={itemProps?.toUpperCaseText}
          formatPrice={itemProps?.formatPrice}
          isTextField={true}
          tabIndex={itemProps.tabIndex}
          isHideLineMark={isHideLineMark}
          cheDoHienThi={!itemProps.inputNumber ? itemProps?.cheDoHienThi : null} //nếu không phải là số thì cheDoHienThi là null
          textPrefix={itemProps?.textPrefix}
          textSuffix={itemProps?.textSuffix}
          anChuKhiIn={itemProps?.anChuKhiIn}
          anSoKhiIn={itemProps?.anSoKhiIn}
          minSize={itemProps?.minSize}
          noSpecialChar={itemProps?.noSpecialChar}
          batDauBangChuSo={itemProps?.batDauBangChuSo}
          plainText={itemProps?.plainText}
        />
      ) : (
        <div className="text-field-config-container">
          {!itemProps.noLabel && (
            <TextEdit
              id={`${component.type}_${component.key}`}
              className={"text-field-label"}
              defaultValue={itemProps.label || label}
              ref={labelRef}
              mode={mode}
              onChange={(value) => {
                setState({
                  labelValue: value,
                });
              }}
              textTransform={textTransform}
              width={itemProps.labelWidth}
              disabled={false} //allow edit in config mode
            />
          )}
          <ContentEditable
            labelWidth={state.labelWidth}
            htmlValue={
              !itemProps.disabled ? defaultValue : "" //Chỉ hiển thị default value khi lấy dữ liệu từ emr
            }
            size={itemProps.size || 500}
            // width={state.width}
            onChange={handleOnChange}
            disabled={true} //disable edit in config mode
            type={itemProps.line > 1 ? "multiple" : "single"}
            extentLine={itemProps.line - 1 || 0}
            contentAlign={itemProps.contentAlign}
            contentColor={itemProps.contentColor || "black"}
            fontSize={itemProps.fontSize || props.fontSize} //parrent font size
            onClick={handleFocus}
            toUpperCaseText={itemProps?.toUpperCaseText}
            isHideLineMark={isHideLineMark}
            showMarkSpanRow={showMarkSpanRow}
            plainText={itemProps?.plainText}
            {...other}
          />
        </div>
      )}
    </Main>
  );
});

TextField.defaultProps = {
  mode: MODE.editing,
  labelText: "",
  form: {},
  formChange: {},
  component: {
    noLabel: false,
  },
  line: {},
};

TextField.propTypes = {
  mode: T.oneOf([MODE.config, MODE.editing]),
  form: T.shape({}),
  formChange: T.shape({}),
  component: T.shape({}),
  line: T.shape({}),
  labelText: T.string,
};

export default TextField;
