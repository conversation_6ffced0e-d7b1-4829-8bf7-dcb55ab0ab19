import React, {
  useEffect,
  useState,
  useImperativeHandle,
  forwardRef,
} from "react";
import T from "prop-types";
import { InputNumber, Input, Row, Col } from "antd";
import { Main } from "./styled";
import { FontColorsOutlined } from "@ant-design/icons";
import {
  FontSizeConfig,
  EditorTool,
  AlignConfig,
} from "components/editor/config";
import { Checkbox, Radio } from "components";
const { FieldName, PickColor } = EditorTool;

import { useTranslation } from "react-i18next";

const ComponentProps = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    fieldName: "",
    noLabel: false,
    disabled: false,
    size: "",
    border: false,
    line: "",
    contentAlign: "left",
    readOnly: false,
    blockSignLevel: 0,
    defaultValue: "",
    defaultFromHIS: false,
    markSpanRow: true,
    lineHeight: "",
    fontSize: "",
    contentColor: "black",
    inputNumber: false,
    maxValue: "",
    minValue: "",
    typeNumber: "int",
    fontWeight: false,
    isDataArray: false,
    toUpperCaseText: false,
    formatPrice: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const { apiFields } = props;

  useImperativeHandle(ref, () => ({
    size: state.size,
    fieldName: state.fieldName,
    noLabel: state.noLabel,
    disabled: state.disabled,
    border: state.border,
    line: state.line,
    labelWidth: state.labelWidth,
    contentAlign: state.contentAlign,
    readOnly: state.readOnly,
    blockSignLevel: state.blockSignLevel,
    defaultValue: state.defaultValue,
    defaultFromHIS: state.defaultFromHIS,
    markSpanRow: state.markSpanRow,
    lineHeight: state.lineHeight,
    fontSize: state.fontSize,
    contentColor: state.contentColor,
    inputNumber: state.inputNumber,
    maxValue: state.maxValue,
    minValue: state.minValue,
    typeNumber: state.typeNumber,
    fontWeight: state.fontWeight,
    isDataArray: state.isDataArray || false,
    toUpperCaseText: state.toUpperCaseText || false,
    formatPrice: state.formatPrice,
    hidePrint: state.hidePrint,
    autoSaveDefaultValue: state.autoSaveDefaultValue,
    fieldName2: state.fieldName2,
    anSoKhiIn: state.anSoKhiIn,
    anChuKhiIn: state.anChuKhiIn,
    heSoQuyDoi: state.heSoQuyDoi,
    minSize: state.minSize,
    noSpecialChar: state.noSpecialChar,
    batDauBangChuSo: state.batDauBangChuSo,
    plainText: state.plainText,
    cheDoHienThi: state.cheDoHienThi || 0,
    textPrefix: state.textPrefix,
    textSuffix: state.textSuffix,
  }));

  useEffect(() => {
    if (props.state.key) {
      setState({
        fieldName: props.state.props.fieldName,
        size: props.state.props.size,
        noLabel: props.state.props.noLabel,
        disabled: props.state.props.disabled,
        border: props.state.props.border,
        line: props.state.props.line,
        labelWidth: props.state.props.labelWidth,
        contentAlign: props.state.props.contentAlign || "left",
        readOnly: props.state.props.readOnly || false,
        blockSignLevel: props.state.props.blockSignLevel || 0,
        defaultValue: props.state.props.defaultValue || "",
        defaultFromHIS: props.state.props.defaultFromHIS || false,
        markSpanRow:
          props.state.props.markSpanRow === undefined
            ? true
            : props.state.props.markSpanRow,
        lineHeight: props.state.props.lineHeight,
        fontSize: props.state.props.fontSize || 12,
        contentColor: props.state.props.contentColor,
        inputNumber: props.state.props.inputNumber,
        maxValue: props.state.props.maxValue,
        minValue: props.state.props.minValue,
        typeNumber: props.state.props.typeNumber || "int",
        fontWeight: props.state.props.fontWeight || false,
        isDataArray: props.state.props.isDataArray || false,
        toUpperCaseText: props.state.props.toUpperCaseText || false,
        formatPrice: props.state.props.formatPrice,
        hidePrint: props.state.props.hidePrint,
        autoSaveDefaultValue: props.state.props.autoSaveDefaultValue,
        fieldName2: props.state.props.fieldName2,
        cheDoHienThi: props.state.props.cheDoHienThi || 0,
        anSoKhiIn: props.state.props.anSoKhiIn,
        anChuKhiIn: props.state.props.anChuKhiIn,
        textPrefix: props.state.props.textPrefix,
        textSuffix: props.state.props.textSuffix,
        heSoQuyDoi: props.state.props.heSoQuyDoi,
        minSize: props.state.props.minSize,
        noSpecialChar: props.state.props.noSpecialChar,
        batDauBangChuSo: props.state.props.batDauBangChuSo,
        plainText: props.state.props.plainText
      });
    }
  }, [props.state]);

  const onChangeValue = (type) => (value) => {
    if (type == "cheDoHienThi") {
      setState({ [type]: value?.target.value })
    } else
      setState({
        [type]: value,
      });
  };
  const onChangeInput = (type) => (e) => {
    setState({
      [type]: e.target.value,
    });
  };

  const onChangeCheckbox = (type) => (e) => {
    onChangeValue(type)(e.target.checked);
  };
  return (
    <Main>
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{"Field name: "}</span>
        </Col>
        <Col span={16}>
          <FieldName
            style={{ width: "100%" }}
            onSelect={onChangeValue("fieldName")}
            value={state.fieldName}
            apiFields={apiFields || []}
          />
        </Col>
        <Col span={8}>
          <span>{"Field name 2: "}</span>
        </Col>
        <Col span={16}>
          <FieldName
            style={{ width: "100%" }}
            onSelect={onChangeValue("fieldName2")}
            value={state.fieldName2}
            apiFields={apiFields || []}
          />
        </Col>
        <Col span={24}>
          FieldName2 dùng để set giá trị khi fieldName bị null
        </Col>
        <Col span={8}>
          <span>{t("editor.khongHienThiNhan")}: </span>
        </Col>
        <Col span={16}>
          <Checkbox
            checked={state.noLabel}
            onChange={onChangeCheckbox("noLabel")}
          />
        </Col>
      </Row>
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{t("editor.doRongNhan")}: </span>
        </Col>
        <Col span={16}>
          <Input
            className="option-content"
            style={{ flex: 1 }}
            value={state.labelWidth}
            onChange={onChangeInput("labelWidth")}
            size={"small"}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.coChu")}</span>
        </Col>
        <Col span={16}>
          <FontSizeConfig
            changeFont={onChangeValue("fontSize")}
            fontSize={state.fontSize}
          />
        </Col>
        <Col span={8}>
          <span>{"Content Align: "}</span>
        </Col>
        <Col span={16}>
          <AlignConfig
            changeAlign={onChangeValue("contentAlign")}
            contentAlign={state.contentAlign}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.boiDamChu")}: </span>
        </Col>
        <Col span={16}>
          <Checkbox
            onChange={onChangeCheckbox("fontWeight")}
            checked={state.fontWeight}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.vietHoa")}: </span>
        </Col>
        <Col span={16}>
          <Checkbox
            onChange={onChangeCheckbox("toUpperCaseText")}
            checked={state.toUpperCaseText}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.mauChu")}: </span>
        </Col>
        <Col span={16}>
          <PickColor
            iconComponent={FontColorsOutlined}
            title={t("editor.chonMauChu")}
            dataColor={state.contentColor || "black"}
            changeColor={onChangeValue("contentColor")}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.khoaOCapKy")}</span>
        </Col>
        <Col span={16}>
          <InputNumber
            value={state.blockSignLevel}
            onChange={onChangeValue("blockSignLevel")}
            size={"small"}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.chiDoc")}: </span>
        </Col>
        <Col span={16}>
          <Checkbox
            onChange={onChangeCheckbox("readOnly")}
            checked={state.readOnly}
          />
        </Col>
        {/* <Col span={8}>
          <span>{"Dữ liệu từ EMR: "}</span>
        </Col>
        <Col span={16}>
          <Checkbox onChange={onChangeDataFormEMR} checked={!state.disabled} />
        </Col> */}
      </Row>
      {/* chỉ hiển thị khi đánh dấu lấy dữ liệu từ EMR */}
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{t("editor.giaTriMacDinh")}</span>
        </Col>
        <Col span={16}>
          <Input.TextArea
            onChange={onChangeInput("defaultValue")}
            rows={3}
            value={state.defaultValue}
            disabled={state.disabled}
            title={t("editor.coHieuLucKhiLayDuLieuTuEMR")}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.tuDongLuuGiaTriMacDinh")}</span>
        </Col>
        <Col span={16}>
          <Checkbox
            onChange={onChangeCheckbox("autoSaveDefaultValue")}
            checked={state.autoSaveDefaultValue}
          />
        </Col>
      </Row>
      {/* chỉ hiển thị khi đánh dấu lấy dữ liệu từ EMR */}
      {/* {!state.disabled && (
        <Row gutter={[12, 12]}>
          <Col span={8}>
            <span>{"Giá trị ban đầu từ HIS: "}</span>
          </Col>
          <Col span={16}>
            <Checkbox
              onChange={onChangeCheckbox("defaultFromHIS")}
              checked={state.defaultFromHIS}
            />
          </Col>
        </Row>
      )} */}
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{t("editor.hienThiDanhDauDong")}:</span>
        </Col>
        <Col span={16}>
          <Checkbox
            onChange={onChangeCheckbox("markSpanRow")}
            checked={state.markSpanRow}
          />
        </Col>
      </Row>
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{t("editor.hienThiVien")}:</span>
        </Col>
        <Col span={16}>
          <Checkbox
            onChange={onChangeCheckbox("border")}
            checked={state.border}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.duLieuMang")}:</span>
        </Col>
        <Col span={16}>
          <Checkbox
            onChange={onChangeCheckbox("isDataArray")}
            checked={state.isDataArray}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.dinhDangTien")}:</span>
        </Col>
        <Col span={16}>
          <Checkbox
            onChange={onChangeCheckbox("formatPrice")}
            checked={state.formatPrice}
          />
        </Col>
        <Col span={8}>
          <span title={t("editor.viDukg=>gam")}>{t("editor.heSoQuyDoi")}:</span>
        </Col>
        <Col span={16}>
          <InputNumber
            value={state.heSoQuyDoi}
            onChange={onChangeValue("heSoQuyDoi")}
            size={"small"}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.anKhiIn")}:</span>
        </Col>
        <Col span={16}>
          <Checkbox
            onChange={onChangeCheckbox("hidePrint")}
            checked={state.hidePrint}
          />
        </Col>
        {!state.inputNumber ?
          <>
            <Col span={8}>
              <span>{t("editor.nhapSo")}:</span>
            </Col>
            <Col span={16}>
              <Checkbox
                onChange={onChangeCheckbox("inputNumber")}
                checked={state.inputNumber}
              />
            </Col>
          </> :
          <fieldset style={{ margin: 7 }}>
            <legend className="flex g-5">
              <span>{t("editor.nhapSo")}</span>
              <Checkbox
                onChange={onChangeCheckbox("inputNumber")}
                checked={state.inputNumber}
              />
            </legend>

            {state.inputNumber && (
              <Row gutter={[6, 6]}>
                <Col span={24}>
                  <Radio.Group
                    onChange={onChangeInput("typeNumber")}
                    value={state.typeNumber}
                  >
                    <Radio value={"int"}>{t("editor.nhapSoNguyen")}</Radio>
                    <Radio value={"float"}>{t("editor.nhapSoThapPhan")}</Radio>
                  </Radio.Group>
                </Col>

                <Col span={8}>
                  <span>{t("editor.giaTriToiDa")}:</span>
                </Col>
                <Col span={16}>
                  <InputNumber
                    value={state.maxValue}
                    onChange={onChangeValue("maxValue")}
                    size={"small"}
                  />
                </Col>
                <Col span={8}>
                  <span>{t("editor.giaTriToiThieu")}</span>
                </Col>
                <Col span={16}>
                  <InputNumber
                    value={state.minValue}
                    onChange={onChangeValue("minValue")}
                    size={"small"}
                  />
                </Col>

                <fieldset style={{ margin: 5 }}>
                  <legend>Chế độ hiển thị</legend>
                  <Radio.Group style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 8
                  }}
                    onChange={onChangeValue("cheDoHienThi")}
                    value={state.cheDoHienThi}
                  >
                    <Radio value={0}>{t("editor.hienThiDungGiaTri")}</Radio>
                    <Radio value={1}>{t("editor.hienThiChu")}</Radio>
                    <Radio value={2}>{t("editor.hienThiSoVaChu")}</Radio>
                  </Radio.Group>
                  <hr />
                  {(state.cheDoHienThi == 1 || state.cheDoHienThi == 2) && (<Row gutter={[6, 6]}>
                    <Col span={8}>
                      <span>{t("editor.tienTo")}</span>
                    </Col>
                    <Col span={16}>
                      <Input
                        value={state.textPrefix}
                        onChange={onChangeInput("textPrefix")}
                        size={"small"}
                      />
                    </Col>
                    <Col span={8}>
                      <span>{t("editor.hauTo")}</span>
                    </Col>
                    <Col span={16}>
                      <Input
                        value={state.textSuffix}
                        onChange={onChangeInput("textSuffix")}
                        size={"small"}
                      />
                    </Col>
                  </Row>)}
                  {state.cheDoHienThi == 2 && (
                    <div className="mt-5 g-5 flex flex-c">
                      <div className="flex g-3">
                        <Checkbox
                          onChange={onChangeCheckbox("anSoKhiIn")}
                          checked={state.anSoKhiIn}
                        /><span>{t("editor.anSoKhiIn")}</span>
                      </div>
                      <div className="flex g-3">
                        <Checkbox
                          onChange={onChangeCheckbox("anChuKhiIn")}
                          checked={state.anChuKhiIn}
                        /><span>{t("editor.anChuKhiIn")}</span>
                      </div>
                    </div>
                  )}
                </fieldset>
              </Row>
            )}
          </fieldset>
        }
        <Col span={8}>
          <span>{t("editor.doCaoDongVanBan")}:</span>
        </Col>
        <Col span={16}>
          <InputNumber
            value={state.lineHeight}
            onChange={onChangeValue("lineHeight")}
            placeholder={1.5}
            min={1}
            step={0.1}
            size={"small"}
          />
        </Col>
      </Row>
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{t("editor.soDong")}:</span>
        </Col>
        <Col span={16}>
          <InputNumber
            value={state.line}
            onChange={onChangeValue("line")}
            size={"small"}
          />
        </Col>

        <Col span={8}>
          <span>{t("editor.soKyTuToiThieu")}:</span>
        </Col>
        <Col span={16}>
          <InputNumber
            size={"small"}
            value={state.minSize}
            onChange={onChangeValue("minSize")}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.soKyTuToiDa")}:</span>
        </Col>
        <Col span={16}>
          <InputNumber
            size={"small"}
            value={state.size}
            onChange={onChangeValue("size")}
          />
        </Col>

        <Col span={8}>
          <span>{t("editor.khongNhapKyTuDacBiet")}:</span>
        </Col>
        <Col span={16}>
          <Checkbox
            onChange={onChangeCheckbox("noSpecialChar")}
            checked={state.noSpecialChar}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.batDauBangChu")}:</span>
        </Col>
        <Col span={16}>
          <Input
            size={"small"}
            value={state.batDauBangChuSo}
            onChange={onChangeInput("batDauBangChuSo")}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.plainText")}:</span>
        </Col>
        <Col span={16}>
          <Checkbox
            onChange={onChangeCheckbox("plainText")}
            checked={state.plainText}
          />
        </Col>
      </Row>
    </Main >
  );
});

ComponentProps.defaultProps = {
  component: {},
  apiFields: [],
};

ComponentProps.propTypes = {
  component: T.shape({}),
  apiFields: T.arrayOf(T.string),
};

export default ComponentProps;
