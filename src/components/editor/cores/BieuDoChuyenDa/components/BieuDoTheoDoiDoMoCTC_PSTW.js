import React, { useRef, useEffect } from "react";
import { CA_LAM_VIEC, generateTimeArray } from "../configData";
import { DoMoCTCGhiChu, DoMoCTCLabel } from "../styled";
import stringUtils from "mainam-react-native-string-utils";
import ModalNhapDoMoCTC from "../modals/ModalNhapDoMoCTC";
import { diffMinutes } from "./utils";

const BieuDoTheoDoiDoMoCTC_PSTW = ({
  canvasWidth = 600,
  canvasHeight = 200,
  onChange,
  duLieu = [],
  caLamViec = CA_LAM_VIEC.THEO_GIO_GHI_BD,
  thoiGianBatDau,
}) => {
  const ARR = generateTimeArray({ stepMinute: 10, caLamViec, thoiGianBatDau });

  const canvasRef = useRef(null);
  const refModalNhapDoMoCTC = useRef(null);
  const refData = useRef();

  useEffect(() => {
    if (duLieu && Array.isArray(duLieu) && duLieu.length > 0) {
      refData.current = duLieu;
    } else {
      //nếu chưa có dữ liệu thì khởi tạo mảng mặc định
      refData.current = ARR.map((item) => ({
        time: item,
        tienTrienDau: duLieu.find((x) => x.time == item)?.tienTrienDau || null,
        doMoCTC: duLieu.find((x) => x.time == item)?.doMoCTC || null,
      }));
    }

    veBieuDo(refData.current);
  }, [duLieu]);

  const veKhungPha = ({
    ctx,
    hours,
    paddingLeft,
    xStep,
    yStep,
    doMoCTCPoints,
    paddingTop,
    height,
    paddingBottom,
    yLevels,
  }) => {
    // =========================
    // Vẽ Pha tiềm tàng & Pha tích cực
    // =========================

    ctx.strokeStyle = "#000";
    ctx.lineWidth = 1;
    ctx.font = "12px Arial";
    ctx.fillStyle = "#000";

    const yTop = paddingTop; // trên cùng biểu đồ
    const yBottom = height - paddingBottom; // trục X

    // Lấy vị trí X cho toàn bộ biểu đồ
    const xStart = paddingLeft;
    const xEnd = paddingLeft + (hours.length - 1) * xStep;

    // Giả sử: Pha tiềm tàng từ 0 -> 3cm
    // Tìm điểm đầu tiên có doMoCTC > 3

    let indexCross3 = doMoCTCPoints.find((p) => p.y >= 3)?.x || null;

    // Nếu có điểm >3 thì tính toạ độ X
    let xCross =
      indexCross3 !== null ? paddingLeft + (indexCross3 * xStep) / 6 : xEnd / 2; // fallback

    // Vẽ khung pha tiềm tàng
    ctx.beginPath();
    ctx.rect(xStart, yTop, xCross - xStart, yBottom - yTop);
    ctx.stroke();
    ctx.fillText("Pha tiềm tàng", xStart + 10, yTop + 15);

    // Vẽ line ngang tại y = 3 trong pha tiềm tàng
    ctx.beginPath();
    ctx.strokeStyle = "black";
    ctx.lineWidth = 1;

    const yLine = yBottom - 3 * yStep;
    ctx.moveTo(xStart, yLine);
    ctx.lineTo(xCross, yLine);
    ctx.stroke();

    // Vẽ khung pha tích cực
    ctx.beginPath();
    ctx.rect(xCross, yTop, xEnd - xCross, yBottom - yTop);
    ctx.stroke();
    ctx.fillText("Pha tích cực", xCross + 10, yTop + 15);
  };

  const veBieuDo = (data) => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");

    const width = canvas.width;
    const height = canvas.height;

    // Padding riêng từng phía
    const paddingTop = 30; // tăng khoảng trống phía trên để chứa pha
    const paddingRight = 10;
    const paddingBottom = 20;
    const paddingLeft = 40;

    // Vẽ nền
    ctx.fillStyle = "#fff";
    ctx.fillRect(0, 0, width, height);

    // Trục và khoảng cách
    const hours = ARR.filter((item, idx) => idx % 6 == 0); //hiển thị cách mỗi 1h
    const yLevels = 10;

    const xStep = (width - paddingLeft - paddingRight) / (hours.length - 1);
    const yStep = (height - paddingTop - paddingBottom) / yLevels;

    // Vẽ các đường dọc theo mốc giờ trục X
    ctx.strokeStyle = "#ccc";
    ctx.lineWidth = 1;

    hours.forEach((hour, i) => {
      const x = paddingLeft + i * xStep;

      ctx.beginPath();
      ctx.moveTo(x, paddingTop);
      ctx.lineTo(x, height - paddingBottom);
      ctx.stroke();
    });

    // Vẽ nhãn trục X
    ctx.fillStyle = "#000";
    ctx.font = "10px Arial";
    hours.forEach((hour, i) => {
      const x = paddingLeft + i * xStep;
      ctx.fillText(hour, x - 15, height - paddingBottom + 15);
    });

    // Vẽ nhãn trục Y
    for (let i = 0; i <= yLevels; i++) {
      const y = height - paddingBottom - i * yStep;
      ctx.fillText(i.toString(), 10, y + 4);
    }

    // Line tại mốc 0
    ctx.beginPath();
    ctx.moveTo(paddingLeft, height - paddingBottom);
    ctx.lineTo(
      paddingLeft + (hours.length - 1) * xStep,
      height - paddingBottom
    );
    ctx.stroke();

    // Line tại mốc 10
    ctx.beginPath();
    ctx.moveTo(paddingLeft, height - paddingBottom - 10 * yStep);
    ctx.lineTo(
      paddingLeft + (hours.length - 1) * xStep,
      height - paddingBottom - 10 * yStep
    );
    ctx.stroke();

    // Vẽ các đường chéo
    for (let col = 0; col < hours.length - 1; col++) {
      for (let row = 2; row < 9; row++) {
        const x1 = paddingLeft + col * xStep;
        const y1 = paddingTop + (9 - row) * yStep;

        const x2 = paddingLeft + (col + 1) * xStep;
        const y2 = paddingTop + (9 - (row + 1)) * yStep;

        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.stroke();
      }
    }

    // Dữ liệu
    const tienTrienDauCTCPoints = data
      .map((item, index) => ({
        y: item.tienTrienDau,
        x: diffMinutes(item.time, data[0].time) / 10,
      }))
      .filter((item) => !!item.y && item.y >= 0 && item.y <= 10);

    const doMoCTCPoints = data
      .map((item, index) => ({
        y: item.doMoCTC,
        x: diffMinutes(item.time, data[0].time) / 10,
      }))
      .filter((item) => !!item.y && item.y >= 0 && item.y <= 10);

    let baoDongPoints = doMoCTCPoints.filter((item) => item.y >= 3);
    if (baoDongPoints.length > 0) {
      baoDongPoints = [baoDongPoints[0]];
    }
    const hanhDongPoints = baoDongPoints
      .map((item) => ({
        y: item.y,
        x: item.x + 4 * 6,
      }))
      .filter((item) => item.x < 12 * 6);

    // Vẽ ●
    ctx.fillStyle = "#000";
    tienTrienDauCTCPoints.forEach((p) => {
      const cx = paddingLeft + (p.x * xStep) / 6;
      const cy = height - paddingBottom - p.y * yStep;
      ctx.beginPath();
      ctx.arc(cx, cy, 2, 0, Math.PI * 2);
      ctx.fill();
    });

    // Vẽ ○
    ctx.strokeStyle = "#000";
    doMoCTCPoints.forEach((p) => {
      const cx = paddingLeft + (p.x * xStep) / 6;
      const cy = height - paddingBottom - p.y * yStep;
      ctx.beginPath();
      ctx.arc(cx, cy, 2, 0, Math.PI * 2);
      ctx.stroke();
    });

    // Vẽ đường nối
    ctx.beginPath();
    tienTrienDauCTCPoints.forEach((p, i) => {
      const cx = paddingLeft + (p.x * xStep) / 6;
      const cy = height - paddingBottom - p.y * yStep;
      if (i === 0) ctx.moveTo(cx, cy);
      else ctx.lineTo(cx, cy);
    });
    ctx.strokeStyle = "#000000";
    ctx.lineWidth = 2;
    ctx.stroke();

    ctx.beginPath();
    doMoCTCPoints.forEach((p, i) => {
      const cx = paddingLeft + (p.x * xStep) / 6;
      const cy = height - paddingBottom - p.y * yStep;
      if (i === 0) ctx.moveTo(cx, cy);
      else ctx.lineTo(cx, cy);
    });
    ctx.strokeStyle = "#008000";
    ctx.lineWidth = 2;
    ctx.stroke();

    // Vẽ đường báo động
    ctx.beginPath();
    baoDongPoints.forEach((p) => {
      const cx1 = paddingLeft + (p.x * xStep) / 6;
      const cy1 = height - paddingBottom - p.y * yStep;

      let cx2 = paddingLeft + (p.x * xStep) / 6 + xStep * (10 - p.y);
      let cy2 = height - paddingBottom - 10 * yStep;
      if (p.x + (10 - p.y) * 6 > 12 * 6) {
        cx2 = paddingLeft + xStep * 12;
        cy2 = height - paddingBottom - (10 - (p.x / 6 - 5)) * yStep;
      }

      ctx.beginPath();
      ctx.moveTo(cx1, cy1);
      ctx.lineTo(cx2, cy2);
      ctx.strokeStyle = "#0eaee4ff";
      ctx.lineWidth = 2;
      ctx.stroke();
    });

    // Vẽ đường hành động
    hanhDongPoints.forEach((p) => {
      const cx1 = paddingLeft + (p.x * xStep) / 6;
      const cy1 = height - paddingBottom - p.y * yStep;

      let cx2 = paddingLeft + (p.x * xStep) / 6 + xStep * (10 - p.y);
      let cy2 = height - paddingBottom - 10 * yStep;
      if (p.x + (10 - p.y) * 6 > 12 * 6) {
        cx2 = paddingLeft + xStep * 12;
        cy2 =
          height - paddingBottom - (10 - (p.x / 6 - (12 - (10 - p.y)))) * yStep;
      }

      ctx.beginPath();
      ctx.moveTo(cx1, cy1);
      ctx.lineTo(cx2, cy2);
      ctx.strokeStyle = "#FF0000";
      ctx.lineWidth = 2;
      ctx.stroke();
    });

    //Vẽ khung pha tiềm tàng và pha tích cực
    veKhungPha({
      ctx,
      hours,
      paddingLeft,
      xStep,
      yStep,
      doMoCTCPoints,
      paddingTop: 10,
      height,
      paddingBottom,
      yLevels,
    });
  };

  const onShowModalNhapDoMoCTC = () => {
    refModalNhapDoMoCTC.current &&
      refModalNhapDoMoCTC.current.show(refData.current, (values) => {
        onChange(values);

        refData.current = values;
        veBieuDo(values);
      });
  };

  return (
    <>
      <div className="flex-item border-div">
        <div className="flex-item-label">
          <div
            style={{
              display: "flex",
              justifyContent: "space-evenly",
              alignItems: "end",
            }}
          >
            <DoMoCTCLabel height={200}>
              <div class="line-horizontal"></div>
              <div class="line"></div>
              <div class="vertical-text">Độ mở cổ tử cung (cm) (X)</div>
              <div class="line"></div>
              <div class="line-horizontal"></div>
            </DoMoCTCLabel>

            <DoMoCTCLabel height={160}>
              <div class="line-horizontal"></div>
              <div class="line"></div>
              <div class="vertical-text">Tiến triển của đầu (O)</div>
              <div class="line"></div>
              <div class="line-horizontal"></div>
            </DoMoCTCLabel>
          </div>
        </div>

        <div>
          <div className="canvas-vital-signs" id={stringUtils.guid()}>
            <canvas
              ref={canvasRef}
              width={canvasWidth}
              height={canvasHeight}
              onClick={onShowModalNhapDoMoCTC}
              style={{ border: "1px solid #ccc" }}
            />
          </div>

          <DoMoCTCGhiChu>
            <div class="legend-item">
              <span class="dot">●</span>Tiến triển của đầu (O)
            </div>
            <div class="legend-item">
              <span class="dot">◉</span>Độ mở CTC (X)
            </div>
            <div style={{ backgroundColor: "#0eaee4ff" }} class="line"></div>
            <div class="legend-item">Báo động</div>
            <div style={{ backgroundColor: "#FF0000" }} class="line"></div>
            <div class="legend-item">Hành động</div>
          </DoMoCTCGhiChu>
        </div>
      </div>
      <ModalNhapDoMoCTC ref={refModalNhapDoMoCTC} />
    </>
  );
};

export default BieuDoTheoDoiDoMoCTC_PSTW;
