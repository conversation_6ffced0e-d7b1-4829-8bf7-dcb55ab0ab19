import nbBienBanHoiChanProvider from "data-access/nb-bien-ban-hoi-chan-provider";
import nbPhieuSoKetProvider from "data-access/nb-phieu-so-ket-provider";
import nbDvChePhamMauProvider from "data-access/nb-dv-che-pham-mau-provider";
import nbKhamTiemChungProvider from "data-access/tiemChung/nb-kham-tiem-chung-provider";
import nbDvThuocChiDinhNgoaiProvider from "data-access/thuoc/nb-dv-thuoc-chi-dinh-ngoai-provider";
import nbDvVatTuProvider from "data-access/nb-dv-vat-tu-provider";
import nbHoSoProvider from "data-access/nb-ho-so-provider";
import nbSangLocSuyDdProvider from "data-access/dinhDuong/nb-sang-loc-suy-dd-provider";
import nbTuVanThuocProvider from "data-access/kho/nb-tu-van-thuoc-provider";
import nbPhieuThuProvider from "data-access/nb-phieu-thu-provider";
import nbPhieuDoiTraProvider from "data-access/nb-phieu-doi-tra-provider";
import nbDvKhamProvider from "data-access/nb-dv-kham-provider";
import nbDvXetNghiemProvider from "data-access/nb-dv-xet-nghiem-provider";
import nbDvCdhaTdcnPtTtProvider from "data-access/nb-dv-cdha-tdcn-pt-tt-provider";
import nbDvThuocProvider from "data-access/nb-dv-thuoc-provider";
import dichVuProvider from "data-access/dich-vu-provider";
import nbChuyenVienProvider from "data-access/noiTru/nb-chuyen-vien-provider";
import nbTamUngProvider from "data-access/nb-tam-ung-provider";
import nbDvNgoaiDieuTriProvider from "data-access/nb-dv-ngoai-dieu-tri-provider";
import nbDvKyThuatProvider from "data-access/nb-dv-ky-thuat-provider";
import nbGoiDvProvider from "data-access/nb-goi-dv-provider";
import nbPhieuLinhSuatAnProvider from "data-access/nb-phieu-linh-suat-an";
import nbDichVuProvider from "data-access/nb-dich-vu-provider";
import nbTiemChungProvider from "data-access/nb-tiem-chung-provider";
import phieuNhapXuatProvider from "data-access/kho/phieu-nhap-xuat-provider";
import nbPhieuTiemChungProvider from "data-access/tiemChung/nb-phieu-tiem-chung-provider";
import nvLichSuKyProvider from "data-access/kySo/nv-lich-su-ky-provider";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import nbPhucHoiCnKhamProvider from "data-access/phcn/nb-phuc-hoi-cn-kham-provider";
import nbLuongGiaPHCNProvider from "data-access/phcn/nb-luong-gia-phcn-provider";
import nbKhamCkMatProvider from "data-access/nb-kham-ck-mat-provider";
import { locPhieuLisPacs } from "utils";
import stringUtils from "mainam-react-native-string-utils";
import {
  GIAY_IN_BIEN_BAN_HOI_CHAN,
  LOAI_BC_THONG_TIN_NB,
  LOAI_BIEU_MAU,
  LOAI_DICH_VU,
  LOAI_IN_BANG_KE_CHI_PHI,
  LIST_PHIEU_IN_BANG_KE,
} from "constants/index";
import { t } from "i18next";
import moment from "moment";
import { cloneDeep, flatten, isArray, uniq } from "lodash";
import editorUtils from "utils/editor-utils";
import pdfUtils from "utils/pdf-utils";
import { message } from "antd";
import nbHenNoiSoiProvider from "data-access/henNoiSoi/nb-hen-noi-soi-provider";
import nbDanhSachLichHenProvider from "data-access/nb-danh-sach-lich-hen-provider";
import nbDichVuKyThuatProvider from "data-access/nb-dich-vu-ky-thuat-provider";
import nbThanhToanProvider from "data-access/nb-thanh-toan-provider";
import nbPhaCheThuocProvider from "data-access/phaCheThuoc/nb-pha-che-thuoc-provider";
import nbPhaCheProvider from "data-access/phaCheThuoc/nb-pha-che-provider";
import nbChuyenKhoaProvider from "data-access/noiTru/nb-chuyen-khoa-provider";
import nbCapPhatThuocProvider from "data-access/kho/nb-cap-phat-thuoc-provider";
import nbPhaCheThuocChiTietProvider from "data-access/noiTru/nb-pha-che-thuoc-chi-tiet-provider";
import goiSoProvider from "data-access/goi-so-provider"

export const getThongTinPhieu = ({
  phieu: item,
  nbDotDieuTriId,
  nbThongTinId,
  chiDinhTuLoaiDichVu,
  dsChiDinhTuLoaiDichVu,
  chiDinhTuDichVuId,
  trangThai,
  showError,
  thietLapBangKeTongHopExcel,
  idLichSuKy,
  thanhToanId,
  loai,
  ngonNguParams = {},
  qrThanhToan,
  ...payload
}) => {
  let promise = null;

  if (
    item.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA &&
    !payload.isInHsba
  ) {
    if (
      !(
        LIST_PHIEU_IN_BANG_KE.includes(item.ma) &&
        thietLapBangKeTongHopExcel?.eval()
      )
    ) {
      promise = new Promise((resolve, reject) => {
        item.id = stringUtils.guid();
        resolve({
          code: 0,
          data: item,
          type: "editor",
        });
      });
      return promise;
    }
  }
  const dsSoPhieu = item?.dsSoPhieu || [];
  const maPhieu = item.ma;
  const id = payload.id;
  let mhParams = {};
  if (item.kySo) {
    mhParams = {
      nbDotDieuTriId,
      chiDinhTuLoaiDichVu,
      dsChiDinhTuLoaiDichVu,
      chiDinhTuDichVuId,
      ...payload,
      kySo: true,
      maPhieuKy: item.ma,
      isInHsba: undefined,
    };
  }

  //kiểm tra trường loại biểu mẫu = phiếu scan (loaiBieuMau = 60)
  //=> khi in phiếu: lấy file theo ds đường dẫn ở từng object: dsSoPhieu.dsDuongDan
  if (item.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_SCAN) {
    // switch (maPhieu) {
    //   case "P220":
    //   case "P221":
    //   case "P222":
    //   case "P223":
    //   case "P327":
    //   case "P328":
    //   case "P329":
    promise = nbHoSoProvider.search({
      page: "",
      size: "",
      nbThongTinId,
      loai: 50,
      dsSoPhieu: dsSoPhieu[0]?.soPhieu,
    });
    // break;
    // default:
    //   if (showError) {
    //     message.error(t("phieuIn.chuaDuocCauHinhThongTinPhieu"));
    //   }
    //   break;
    // }
  } else if (
    item.loaiBieuMau !== LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA &&
    item?.isPhieuDaKy
  ) {
    promise = nvLichSuKyProvider.getBaoCaoDaKy({
      id: idLichSuKy || dsSoPhieu[0]?.lichSuKyId,
      chuKySo: payload?.chuKySo,
    });
  } else {
    switch (maPhieu) {
      case "P001":
        // promise = nbDvKhamProvider.phieuKhamBenh({
        //   nbDotDieuTriId,
        //   chiDinhTuLoaiDichVu,
        //   dsChiDinhTuLoaiDichVu,
        //   chiDinhTuDichVuId,
        // });
        promise = nbDvKhamProvider.phieuKhamBenhTheoId({
          nbDvKhamId: dsSoPhieu[0]?.soPhieu,
        });
        break;
      case "P002":
        promise = nbDvKhamProvider.phieuKhamBenhTheoId({
          nbDvKhamId: chiDinhTuDichVuId,
        });
        break;
      case "P004":
        if (chiDinhTuDichVuId)
          promise = nbDvKhamProvider.getPhieuKhamBenhKetLuan(chiDinhTuDichVuId);
        else
          promise = nbDvKhamProvider.getPhieuKhamBenhKetLuanParams({
            nbDotDieuTriId,
          });
        break;
      case "P005":
        promise = nbDvThuocProvider.getDonChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          phieuNhapXuatId: payload?.phieuNhapXuatId,
          maPhieuIn: maPhieu,
          maViTri: payload?.maViTri2 || payload?.maViTri,
        });
        break;
      case "P006":
        promise = nbDotDieuTriProvider.getPhieuGiuTheBHYT(nbDotDieuTriId);
        break;
      case "P007":
        promise = nbDotDieuTriProvider.getCamKetDieuTriCovid(nbDotDieuTriId);
        break;
      case "P008":
        promise = dichVuProvider.getVongTayNguoiBenh(nbDotDieuTriId);
        break;
      case "P742":
      case "P743":
        promise = dichVuProvider.getVongTayNguoiBenh(nbDotDieuTriId, {
          conThu: dsSoPhieu[0]?.soPhieu,
        });
        break;
      case "P009":
        promise = nbDvKhamProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          ...(payload.isInHsba
            ? { dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON }
            : {}),
        });
        break;
      case "P010":
        promise = nbDvXetNghiemProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
        });
        break;
      case "P011":
      case "P017":
      case "P020":
      case "1286":
        promise = nbDvCdhaTdcnPtTtProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          loaiDichVu: 30, //phiếu chỉ định cdha
          dsPhieuInId: dsSoPhieu[0]?.soPhieu,
        });
        break;
      case "P012":
        promise = nbDvXetNghiemProvider.getPhieuKetQua({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
        });
        break;
      case "P014":
        promise = nbDvCdhaTdcnPtTtProvider.getPhieuKetQua({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
        });
        break;
      case "P015":
        promise = nbDvKhamProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          ...(payload.isInHsba
            ? { dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON }
            : {}),
        });
        break;
      case "P016":
        promise = nbDvXetNghiemProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          dsPhieuInId: dsSoPhieu?.[0]?.soPhieu,
        });
        break;
      case "P018":
        promise = nbDvKhamProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          dsPhieuInId: dsSoPhieu[0]?.soPhieu,
          ...(payload.isInHsba
            ? { dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON }
            : {}),
        });
        break;
      case "P019":
      case "P789":
        promise = nbDvXetNghiemProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          dsPhieuInId: dsSoPhieu[0]?.soPhieu,
        });
        break;

      case "P790":
        promise = nbDvCdhaTdcnPtTtProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          loaiDichVu: 40, //phiếu phẫu thuật thủ thuật
        });
        break;
      case "P046":
        promise = nbDvCdhaTdcnPtTtProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          loaiDichVu: 40, //phiếu phẫu thuật thủ thuật
          ...(ngonNguParams || {}),
        });
        break;
      case "P047":
        promise = nbDvCdhaTdcnPtTtProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu,
          dsPhieuInId: dsSoPhieu[0]?.soPhieu,
          loaiDichVu: 40, //phiếu phẫu thuật thủ thuật
          ...(ngonNguParams || {}),
        });
        break;
      case "P021":
        promise = nbDvCdhaTdcnPtTtProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          loaiDichVu: 40, //phiếu phẫu thuật thủ thuật
          dsPhieuInId: dsSoPhieu[0]?.soPhieu,
        });
        break;

      case "P013":
        promise = new Promise((resolve, reject) => {
          nbDvCdhaTdcnPtTtProvider
            .getPhieuKetQua({
              nbDotDieuTriId,
              chiDinhTuLoaiDichVu,
              dsChiDinhTuLoaiDichVu,
              chiDinhTuDichVuId,
            })
            .then((s) => {
              s.data = locPhieuLisPacs(s.data, {
                isLocPhieu: true,
                allData: true,
                isCdha: true,
                isAllHisLisPacs: true,
              });
              resolve(s);
            })
            .catch((e) => {
              resolve({
                code: 1,
              });
            });
        });
        break;
      case "P024":
      case "P289":
        promise = nbDvCdhaTdcnPtTtProvider.phieuPTTT(dsSoPhieu[0]?.soPhieu);
        break;

      case "P025":
        promise = nbDvThuocProvider.getDonChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          dsDonThuocId: dsSoPhieu[0]?.soPhieu,
          maPhieuIn: maPhieu,
          maViTri: payload?.maViTri2 || payload?.maViTri,
        });
        break;
      case "P027":
        promise = nbDvThuocProvider.getDonThuocTongHop({
          nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu:
            dsChiDinhTuLoaiDichVu || dsSoPhieu[0]?.chiDinhTuLoaiDichVu,
          chiDinhTuDichVuId:
            chiDinhTuDichVuId || dsSoPhieu[0]?.chiDinhTuDichVuId,
          maPhieuIn: maPhieu,
          maViTri: payload?.maViTri2 || payload?.maViTri,
          ...(ngonNguParams || {}),
        });
        break;
      case "P028":
      case "P058":
      case "P227":
      case "P319":
      case "P320":
      case "P321":
      case "P492":
      case "P801":
      case "P802":
        promise = nbDvThuocProvider.getDonChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          ...(["P801", "P802"].includes(maPhieu)
            ? {}
            : { dsDonThuocId: dsSoPhieu[0]?.soPhieu }),
          maPhieuIn: maPhieu,
          maViTri: payload?.maViTri2 || payload?.maViTri,
          ...(maPhieu == "P801"
            ? {
                loaiDonThuoc: dsSoPhieu[0]?.loaiDonThuoc,
                thuocChiDinhNgoai: true,
                thuocThuong: dsSoPhieu[0]?.thuocThuong,
              }
            : {}),
          ...(maPhieu == "P802" ? { donThuocTrang: true } : {}),
          ...(ngonNguParams || {}),
        });
        break;
      case "P029":
        promise = nbDichVuKyThuatProvider.getPhieuHuongDan({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
        });
        break;
      case "P030":
        promise = nbDichVuKyThuatProvider.getPhieuHuongDan({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          ...payload,
        });
        break;
      case "P031":
        promise = nbDichVuKyThuatProvider.getPhieuHuongDan({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
        });
        break;
      case "P032":
      case "P107":
      case "P178":
      case "P541":
      case "P727":
      case "P746":
      case "P747":
        promise = pdfUtils.headLessLoad(
          editorUtils.getLinkHeadless({
            ma: maPhieu,
            nbDotDieuTriId,
            mhParams,
            baoCaoId: item.baoCaoId,
          })
        );
        break;
      case "P033":
        if (dsSoPhieu[0]?.soPhieu)
          promise = nbPhieuThuProvider.getInPhieuThu({
            phieuThuId: dsSoPhieu[0]?.soPhieu,
          });
        else
          return new Promise((resolve, reject) =>
            resolve({
              data: { message: t("thuNgan.khongTonTaiPhieuThuPhuHop") },
              code: 1,
            })
          );
        break;
      case "P034":
        if (dsSoPhieu[0]?.soPhieu)
          promise = nbPhieuDoiTraProvider.getInPhieuChi(dsSoPhieu[0]?.soPhieu);
        break;
      case "P036":
      case "P1295":  
        promise = nbDotDieuTriProvider.getPhieuRaVien({
          nbDotDieuTriId,
          ...(ngonNguParams || {}),
        });
        break;
      case "P035":
      case "P037":
      case "P038":
      case "P039":
      case "P042":
      case "P043":
      case "P056":
      case "P074":
      case "P077":
      case "P079":
      case "P090":
      case "P091":
      case "P095":
      case "P097":
      case "P098":
      case "P099":
      case "P100":
      case "P101":
      case "P102":
      case "P105":
      case "P106":
      case "P111":
      case "P113":
      case "P115":
      case "P117":
      case "P118":
      case "P119":
      case "P120":
      case "P122":
      case "P123":
      case "P124":
      case "P125":
      case "P129":
      case "P131":
      case "P141":
      case "P146":
      case "P150":
      case "P154":
      case "P156":
      case "P157":
      case "P162":
      case "P163":
      case "P172":
      case "P174":
      case "P175":
      case "P179":
      case "P180":
      case "P196":
      case "P213":
      case "P217":
      case "P218":
      case "P231":
      case "P232":
      case "P240":
      case "P241":
      case "P251":
      case "P283":
      case "P284":
      case "P285":
      case "P286":
      case "P295":
      case "P296":
      case "P297":
      case "P299":
      case "P310":
      case "P312":
      case "P313":
      case "P314":
      case "P315":
      case "P316":
      case "P317":
      case "P318":
      case "P356":
      case "P387":
      case "P388":
      case "P396":
      case "P404":
      case "P405":
      case "P411":
      case "P412":
      case "P419":
      case "P420":
      case "P427":
      case "P428":
      case "P429":
      case "P432":
      case "P433":
      case "P434":
      case "P435":
      case "P436":
      case "P437":
      case "P439":
      case "P446":
      case "P447":
      case "P448":
      case "P449":
      case "P455":
      case "P456":
      case "P463":
      case "P464":
      case "P473":
      case "P474":
      case "P475":
      case "P476":
      case "P478":
      case "P503":
      case "P504":
      case "P509":
      case "P510":
      case "P511":
      case "P512":
      case "P515":
      case "P516":
      case "P517":
      case "P520":
      case "P521":
      case "P522":
      case "P523":
      case "P524":
      case "P542":
      case "P543":
      case "P547":
      case "P548":
      case "P553":
      case "P559":
      case "P560":
      case "P565":
      case "P566":
      case "P567":
      case "P573":
      case "P574":
      case "P575":
      case "P580":
      case "P581":
      case "P582":
      case "P588":
      case "P589":
      case "P590":
      case "P596":
      case "P597":
      case "P598":
      case "P606":
      case "P628":
      case "P636":
      case "P605":
      case "P633":
      case "P634":
      case "P591":
      case "P641":
      case "P642":
      case "P645":
      case "P658":
      case "P660":
      case "P673":
      case "P674":
      case "P646":
      case "P684":
      case "P706":
      case "P708":
      case "P709":
      case "P710":
      case "P713":
      case "P714":
      case "P720":
      case "P721":
      case "P722":
      case "P723":
      case "P724":
      case "P725":
      case "P735":
      case "P751":
      case "P752":
      case "P766":
      case "P767":
      case "P768":
      case "P773":
      case "P754":
      case "P756":
      case "P755":
      case "P783":
      case "P782":
      case "P810":
      case "P821":
      case "P822":
      case "P838":
      case "P839":
      case "P840":
      case "P842":
      case "P848":
      case "P849":
      case "P852":
      case "P853":
      case "P857":
      case "P860":
      case "P861":
      case "P889":
      case "P883":
      case "P912":
      case "P903":
      case "P893":
      case "P920":
      case "P921":
      case "P909":
      case "P948":
      case "P939":
      case "P982":
      case "P980":
      case "P986":
      case "P908":
      case "P1081":
      case "P1114":
      case "P1115":
        promise = pdfUtils.headLessLoad(
          editorUtils.getLinkHeadless({
            ma: maPhieu,
            nbDotDieuTriId,
            mhParams,
            baoCaoId: item.baoCaoId,
            ...(maPhieu === "P565"
              ? { loai: 10 }
              : maPhieu === "P566"
              ? { loai: 20 }
              : {}),
          })
        );
        break;
      case "P086":
      case "P1288":
        promise = pdfUtils.headLessLoad(
          editorUtils.getLinkHeadless({
            ma: maPhieu,
            nbDotDieuTriId,
            mhParams,
            baoCaoId: item.baoCaoId,
            capPhatThuoc: maPhieu == "P1288",
          })
        );
        break;
      case "P890":
      case "P891":
      case "P931":
      case "P938":
      case "P925":
      case "P935":
        promise = pdfUtils.headLessLoad(
          editorUtils.getLinkHeadless({
            ma: maPhieu,
            nbDotDieuTriId,
            mhParams,
            baoCaoId: item.baoCaoId,
          })
        );
        break;
      case "P390":
      case "P398":
      case "P399":
        promise = pdfUtils.headLessLoad(
          editorUtils.getLinkHeadless({
            ma: maPhieu,
            nbDotDieuTriId,
            mhParams,
            baoCaoId: item.baoCaoId,
            ...payload,
          })
        );
        break;
      case "P116":
      case "P153": // Phiếu tóm tắt BA
      case "P155":
      case "P311":
      case "P127":
      case "P173":
      case "P126":
      case "P427":
      case "P417":
      case "P419":
      case "P428":
      case "P420":
      case "P425":
      case "P418":
      case "P426":
      case "P424":
      case "P471":
      case "P472":
      case "P748":
      case "P749":
        promise = pdfUtils.headLessLoad(
          editorUtils.getLinkHeadless({
            ma: maPhieu,
            nbDotDieuTriId,
            mhParams,
            baoCaoId: item.baoCaoId,
          })
        );
        break;
      case "P254":
      case "P282":
      case "P281":
      case "P685":
      case "P686":
        promise = pdfUtils.headLessLoad(
          editorUtils.getLinkHeadless({
            ma: maPhieu,
            nbDvKhamId: chiDinhTuDichVuId,
            mhParams,
            baoCaoId: item.baoCaoId,
          })
        );
        break;
      case "P040":
        promise = pdfUtils.headLessLoad(
          editorUtils.getLinkHeadless({
            ma: maPhieu,
            nbGoiDvId: item.nbGoiDvId,
            mhParams,
            baoCaoId: item.baoCaoId,
          })
        );
        break;
      case "P041":
      case "P044":
      case "P073":
      case "P088":
      case "P108":
      case "P135":
      case "P139":
      case "P143":
      case "P144":
      case "P161":
      case "P164":
      case "P165":
      case "P167":
      case "P168":
      case "P186":
      case "P188":
      case "P229":
      case "P230":
      case "P234":
      case "P235":
      case "P236":
      case "P237":
      case "P238":
      case "P239":
      case "P242":
      case "P244":
      case "P245":
      case "P259":
      case "P252":
      case "P253":
      case "P256":
      case "P257":
      case "P268":
      case "P273":
      case "P271":
      case "P277":
      case "P280":
      case "P322":
      case "P323":
      case "P324":
      case "P361":
      case "P366":
      case "P369":
      case "P372":
      case "P373":
      case "P375":
      case "P376":
      case "P377":
      case "P403":
      case "P407":
      case "P408":
      case "P423":
      case "P413":
      case "P416":
      case "P397":
      case "P452":
      case "P458":
      case "P453":
      case "P457":
      case "P451":
      case "P459":
      case "P460":
      case "P461":
      case "P441":
      case "P483":
      case "P479":
      case "P381":
      case "P500":
      case "P501":
      case "P502":
      case "P488":
      case "P484":
      case "P519":
      case "P518":
      case "P525":
      case "P526":
      case "P527":
      case "P528":
      case "P529":
      case "P530":
      case "P538":
      case "P550":
      case "P551":
      case "P137":
      case "P138":
      case "P562":
      case "P569":
      case "P571":
      case "P576":
      case "P578":
      case "P585":
      case "P584":
      case "P592":
      case "P593":
      case "P622":
      case "P626":
      case "P638":
      case "P631":
      case "P667":
      case "P670":
      case "P676":
      case "P665":
      case "P679":
      case "P695":
      case "P698":
      case "P701":
      case "P703":
      case "P705":
      case "P711":
      case "P731":
      case "P732":
      case "P738":
      case "P740":
      case "P744":
      case "P745":
      case "P753":
      case "P758":
      case "P759":
      case "P760":
      case "P763":
      case "P764":
      case "P765":
      case "P769":
      case "P770":
      case "P771":
      case "P772":
      case "P776":
      case "P778":
      case "P779":
      case "P780":
      case "P791":
      case "P796":
      case "P799":
      case "P805":
      case "P806":
      case "P808":
      case "P815":
      case "P812":
      case "P823":
      case "P825":
      case "P827":
      case "P830":
      case "P835":
      case "P836":
      case "P844":
      case "P863":
      case "P866":
      case "P874":
      case "P885":
      case "P897":
      case "P899":
      case "P902":
      case "P910":
      case "P913":
      case "P934":
      case "P1130":
      case "P942":
      case "P950":
      case "P951":
      case "P955":
      case "P958":
      case "P960":
      case "P962":
      case "P964":
      case "P968":
      case "P972":
      case "P977":
      case "P984":
      case "P991":
      case "P995":
      case "P1007":
      case "P1029":
      case "P1048":
      case "P1053":
      case "P1073":
      case "P1062":
      case "P1058":
      case "P1031":
      case "P1070":
      case "P1068":
      case "P1037":
      case "P1033":
      case "P1089":
      case "P1085":
      case "P976":
      case "P1110":
      case "P1117":
      case "P1021":
      case "P1077":
      case "P1066":
      case "P873":
      case "P1133":
      case "P1094":
      case "P1195":
      case "P1196":
      case "P1198":
      case "P1197":
      case "P1237":
      case "P1244":
      case "P151":
      case "P11131":
      case "P11128":
      case "P11130":
      case "P11125":
      case "P1258":
      case "P967":
      case "P11133":
      case "P1291":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.soPhieu,
              nbDotDieuTriId,
              nbThongTinId,
              mhParams,
              baoCaoId: item.baoCaoId,
              maBaoCao: item.maBaoCao,
              ...payload,
            })
          );
        }
        break;
      case "P233":
      case "P1289":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.soPhieu,
              nbDotDieuTriId,
              nbThongTinId,
              mhParams,
              baoCaoId: item.baoCaoId,
              maBaoCao: item.maBaoCao,
              capPhatThuoc: maPhieu == "P233" ? false : true,
              ...payload,
            })
          );
        }
        break;
      case "P1238":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.soPhieu,
              nbDotDieuTriId,
              nbThongTinId,
              mhParams,
              baoCaoId: item.baoCaoId,
              maBaoCao: item.maBaoCao,
              ...payload,
            })
          );
        }
        break;
      case "P505":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.soPhieu,
              nbDotDieuTriId,
              nbThongTinId,
              mhParams,
              baoCaoId: item.baoCaoId,
              dsTrangThaiHoan: [0, 10],
              ...payload,
            })
          );
        }
        break;
      case "P266":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.phieuXuatId) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.phieuXuatId,
              nbDotDieuTriId,
              mhParams,
              baoCaoId: item.baoCaoId,
              ...payload,
            })
          );
        }
        break;
      case "P614":
      case "P690":
      case "P691":
      case "P692":
      case "P693":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.soPhieu,
              nbDotDieuTriId,
              mhParams,
              baoCaoId: item.baoCaoId,
              ...payload,
            })
          );
        }
        break;
      case "P368":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.chiDinhTuDichVuId,
              nbDotDieuTriId,
              mhParams,
              baoCaoId: item.baoCaoId,
              ...payload,
            })
          );
        }
        break;
      case "P248":
      case "P249":
      case "P250":
      case "P370":
      case "P386":
      case "P438":
      case "P440":
      case "P466":
      case "P467":
      case "P539":
      case "P945":
      case "P911":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.soPhieu,
              thoiGianThucHien: item?.dsSoPhieu[0]?.thoiGianThucHien,
              tuThoiGian: item?.dsSoPhieu[0]?.tuThoiGian,
              denThoiGian: item?.dsSoPhieu[0]?.denThoiGian,
              nbDotDieuTriId,
              mhParams,
              baoCaoId: item.baoCaoId,
            })
          );
        }
        break;
      case "P623":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.soPhieu,
              ngayTheoDoi: item?.dsSoPhieu[0]?.ngayTheoDoi,
              khoaId: item?.dsSoPhieu[0]?.ten1,
              loaiTheoDoi: item?.dsSoPhieu[0]?.ten2,
              nbDotDieuTriId,
              mhParams,
              baoCaoId: item.baoCaoId,
            })
          );
        }
        break;
      case "P379":
        promise = pdfUtils.headLessLoad(
          editorUtils.getLinkHeadless({
            ma: maPhieu,
            nbDotDieuTriId,
            khoaChiDinhId: payload.khoaChiDinhId,
            baoCaoId: item.baoCaoId,
          })
        );
        break;
      case "P421":
      case "P719":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
          const { thoiGianThucHien, khoaChiDinhId } = item?.dsSoPhieu[0] || {};
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.soPhieu,
              nbDotDieuTriId,
              nbThongTinId,
              mhParams,
              thoiGianThucHien: thoiGianThucHien
                ? moment(thoiGianThucHien).format("YYYY-MM-DD HH:mm:00")
                : "",
              khoaChiDinhId,
              baoCaoId: item.baoCaoId,
              ...payload,
            })
          );
        }
        break;
      case "P290":
        if (item.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
          if (item?.dsSoPhieu && item?.dsSoPhieu[0]) {
            promise = pdfUtils.headLessLoad(
              editorUtils.getLinkHeadless({
                ma: maPhieu,
                soPhieu: item?.dsSoPhieu[0]?.soPhieu,
                thoiGianThucHien: item?.dsSoPhieu[0]?.thoiGianThucHien,
                tuThoiGian: item?.dsSoPhieu[0]?.tuThoiGian,
                denThoiGian: item?.dsSoPhieu[0]?.denThoiGian,
                nbDotDieuTriId,
                baoCaoId: item.baoCaoId,
                mhParams,
              })
            );
          }
        } else {
          let thoiGianThucHien = item?.dsSoPhieu[0]?.thoiGianThucHien;
          let tuThoiGianThucHien = thoiGianThucHien
            ? moment(thoiGianThucHien)
                .subtract(25, "days")
                .format("YYYY-MM-DD 00:00:00")
            : null;
          let denThoiGianThucHien = thoiGianThucHien
            ? moment(thoiGianThucHien).format("YYYY-MM-DD 23:59:59")
            : null;

          promise = nbDichVuProvider.inPhieuCongKhai({
            nbDotDieuTriId,
            tuThoiGianThucHien,
            denThoiGianThucHien,
            khoaChiDinhId: item?.dsSoPhieu[0]?.soPhieu,
            dsLoaiDichVu: [
              LOAI_DICH_VU.KHAM,
              LOAI_DICH_VU.XET_NGHIEM,
              LOAI_DICH_VU.CDHA,
              LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            ],
          });
        }
        break;
      case "P261":
      case "P415":
      case "P507":
      case "P513":
      case "P514":
      case "P556":
      case "P557":
      case "P558":
      case "P793":
      case "P818":
      case "P834":
      case "P845":
      case "P923":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.soPhieu,
              thoiGianThucHien: item?.dsSoPhieu[0]?.thoiGianThucHien,
              khoaChiDinhId: item?.dsSoPhieu[0]?.khoaChiDinhId,
              nbDotDieuTriId,
              baoCaoId: item.baoCaoId,
              mhParams,
            })
          );
        }
        break;
      case "P989":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.soPhieu,
              thoiGianThucHien: item?.dsSoPhieu[0]?.thoiGianThucHien,
              khoaChiDinhId: item?.dsSoPhieu[0]?.khoaChiDinhId,
              nbDotDieuTriId,
              baoCaoId: item.baoCaoId,
              mhParams,
            })
          );
        }
        break;
      case "P507":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.soPhieu,
              thoiGianThucHien: item?.dsSoPhieu[0]?.thoiGianThucHien,
              khoaChiDinhId: item?.dsSoPhieu[0]?.khoaChiDinhId,
              nbDotDieuTriId,
              baoCaoId: item.baoCaoId,
              mhParams,
            })
          );
        }
        break;
      case "P087":
      case "P228":
      case "P487":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.soPhieu,
              chiDinhTuDichVuId: item?.dsSoPhieu[0]?.chiDinhTuDichVuId,
              chiDinhTuLoaiDichVu: item?.dsSoPhieu[0]?.chiDinhTuLoaiDichVu,
              nbDotDieuTriId,
              mhParams,
              baoCaoId: item.baoCaoId,
              ...payload,
            })
          );
        }
        break;
      case "P045":
      case "P443":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
          promise = nbTamUngProvider.inPhieuTamUng(item?.dsSoPhieu[0]?.soPhieu);
        }
        break;
      case "P1099":
      case "P1100":
        promise = nbTamUngProvider.inPhieuTamUng(
          id ?? item?.dsSoPhieu[0]?.soPhieu,
          {
            maViTri: payload?.maViTri,
            maPhieuIn: payload?.maPhieuIn,
          }
        );
        break;
      case "P050":
        promise = nbDvXetNghiemProvider.getPhieuKetQua({
          nbDotDieuTriId,
        });
        break;
      case "P048":
      case "P057":
      case "P219":
        switch (dsSoPhieu[0]?.loaiDichVu) {
          case LOAI_DICH_VU.KHAM:
            promise = nbDvKhamProvider.getPhieuChiDinh({
              nbDotDieuTriId,
              chiDinhTuLoaiDichVu,
              dsChiDinhTuLoaiDichVu,
              chiDinhTuDichVuId,
              dsPhieuInId: dsSoPhieu[0]?.soPhieu,
              inPhieuChiDinh: payload?.inPhieuChiDinh,
              dsNbDichVuId: payload?.dsNbDichVuId,
            });
            break;
          case LOAI_DICH_VU.XET_NGHIEM:
            promise = nbDvXetNghiemProvider.getPhieuChiDinh({
              nbDotDieuTriId,
              chiDinhTuLoaiDichVu,
              dsChiDinhTuLoaiDichVu,
              chiDinhTuDichVuId,
              dsPhieuInId: dsSoPhieu[0]?.soPhieu,
              inPhieuChiDinh: payload?.inPhieuChiDinh,
              dsNbDichVuId: payload?.dsNbDichVuId,
            });
            break;
          case LOAI_DICH_VU.CDHA:
          case LOAI_DICH_VU.PHAU_THUAT_THU_THUAT:
            promise = nbDvCdhaTdcnPtTtProvider.getPhieuChiDinh({
              nbDotDieuTriId,
              chiDinhTuLoaiDichVu,
              dsChiDinhTuLoaiDichVu,
              chiDinhTuDichVuId,
              dsPhieuInId: dsSoPhieu[0]?.soPhieu,
              inPhieuChiDinh: payload?.inPhieuChiDinh,
              dsNbDichVuId: payload?.dsNbDichVuId,
            });
            break;

          default:
            break;
        }
        break;
      // case "P220":
      // case "P221":
      // case "P222":
      // case "P223":
      // case "P327":
      // case "P328":
      // case "P329":
      //   promise = nbHoSoProvider.search({
      //     page: "",
      //     size: "",
      //     nbThongTinId,
      //     loai: 50,
      //     dsSoPhieu: dsSoPhieu[0]?.soPhieu,
      //   });
      //   break;
      case "P052":
        promise = nbPhieuDoiTraProvider.phieuYeuCauHoanDoi({
          nbDotDieuTriId,
          trangThai,
        });
        break;
      case "P1253":
        promise = nbPhieuDoiTraProvider.phieuYeuCauHoanDoi({
          nbDotDieuTriId,
          phieuDoiTraId: chiDinhTuDichVuId,
        });
        break;
      case "P1001":
        promise = nbPhieuDoiTraProvider.getGiayDeNghiDoiTraDv(id);
        break;
      case "P053":
        promise = nbDvKhamProvider.inGiayKskNb({
          nbDotDieuTriId,
          id: chiDinhTuDichVuId,
          ...payload,
        });
        break;
      case "P054":
      case "P1302":  
        promise = nbChuyenVienProvider.getPhieuChuyenVienById(
          id || dsSoPhieu[0]?.soPhieu
        );
        break;
      case "P055":
      case "P1298":  
        promise = nbDotDieuTriProvider.getPhieuHenKham(nbDotDieuTriId);
        break;
      case "P391":
        promise = nbDotDieuTriProvider.getGiayHenDieuTri(nbDotDieuTriId);
        break;
      case "P059":
      case "P064":
        promise = nbDvCdhaTdcnPtTtProvider.phieuPTTT(
          id || dsSoPhieu[0]?.soPhieu
        );
        break;
      case "P060":
      case "P065":
        if (chiDinhTuLoaiDichVu == LOAI_DICH_VU.NOI_TRU || payload.isInHsba) {
          promise = nbDvCdhaTdcnPtTtProvider.giayChungNhanPTTTNoiTru({
            nbDotDieuTriId,
            tuThoiGianThucHien: payload?.tuThoiGianThucHien,
            denThoiGianThucHien: payload?.denThoiGianThucHien,
            soPhieu: dsSoPhieu[0]?.soPhieu,
          });
        } else {
          promise = nbDvCdhaTdcnPtTtProvider.giayChungNhanPTTT({
            id: id || dsSoPhieu[0]?.soPhieu,
            tuThoiGianThucHien: payload?.tuThoiGianThucHien,
            denThoiGianThucHien: payload?.denThoiGianThucHien,
          });
        }

        break;
      case "P062":
      case "P554":
        if (thietLapBangKeTongHopExcel?.eval()) {
          promise = nbDotDieuTriProvider.getBangKeChiPhi({
            nbDotDieuTriId,
            loai: LOAI_IN_BANG_KE_CHI_PHI.TONG_HOP,
            maBaoCao: "bang_ke_chi_phi_tong_hop",
          });
        } else {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              nbDotDieuTriId,
              mhParams,
            })
          );
        }
        break;
      case "P678":
        if (thietLapBangKeTongHopExcel?.eval()) {
          promise = nbDotDieuTriProvider.getBangKeChiPhi({
            nbDotDieuTriId,
            loai: LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU,
            maBaoCao: item.maBaoCao,
          });
        } else {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              nbDotDieuTriId,
              mhParams,
            })
          );
        }
        break;
      case "P677":
        if (thietLapBangKeTongHopExcel?.eval()) {
          promise = nbDotDieuTriProvider.getBangKeChiPhi({
            nbDotDieuTriId,
            loai: LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU_BH,
            maBaoCao: item.maBaoCao,
          });
        } else {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              nbDotDieuTriId,
              mhParams,
            })
          );
        }
        break;
      case "P568":
        promise = nbDotDieuTriProvider.getBangKeChiPhi({
          nbDotDieuTriId,
          loai: LOAI_IN_BANG_KE_CHI_PHI.NOI_TRU_KHONG_BAO_HIEM,
        });

        break;
      case "P063":
        promise = nbDvNgoaiDieuTriProvider.getPhieuChiDinh({
          nbDotDieuTriId,
        });
        break;
      case "P066":
        promise = nbDvNgoaiDieuTriProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsNbDichVuId: dsSoPhieu[0]?.soPhieu,
        });
        break;
      case "P067":
        promise = nbDvNgoaiDieuTriProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu,
          dsNbDichVuId: payload?.dsNbDichVuId,
        });
        break;
      case "P068":
        promise = nbDvNgoaiDieuTriProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
        });
        break;
      case "P069":
      case "P265":
        promise = nbDvCdhaTdcnPtTtProvider.bangKeChiPhiThuocTrongPT(
          id || dsSoPhieu[0]?.soPhieu
        );
        break;
      case "P070":
      case "P260":
        promise = nbDvCdhaTdcnPtTtProvider.bangKeChiPhiVatTuTrongPT(
          id || dsSoPhieu[0]?.soPhieu
        );
        break;
      case "P071":
        promise = nbDvCdhaTdcnPtTtProvider.phieuThanhToanPT(
          id || dsSoPhieu[0]?.soPhieu
        );
        break;
      case "P072":
        promise = nbDvNgoaiDieuTriProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
        });
        break;
      case "P075":
        promise = nbDvKyThuatProvider.getPhieuChiDinhTongHop({
          nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu:
            dsChiDinhTuLoaiDichVu || dsSoPhieu[0]?.chiDinhTuLoaiDichVu,
          chiDinhTuDichVuId:
            chiDinhTuDichVuId || dsSoPhieu[0]?.chiDinhTuDichVuId,
          inPhieuChiDinh: payload?.inPhieuChiDinh,
          dsNbDichVuId: payload?.dsNbDichVuId,
          qrThanhToan
        });
        break;
      case "P082":
      case "P081":
      case "P080":
        promise = nbDvKyThuatProvider.getPhieuXacNhanThucHienDichVu({
          nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu,
          ...payload,
        });
        break;
      case "P061":
        if (payload.nbGoiDvId) {
          promise = nbGoiDvProvider.getPhieuTongHop({
            nbGoiDvId: payload.nbGoiDvId,
          });
        }
        break;
      case "P263":
        promise = nbGoiDvProvider.getTheLieuTrinh(
          +item.dsSoPhieu?.[0]?.soPhieu
        );
        break;
      case "P084":
      case "P085":
        promise = nbPhieuLinhSuatAnProvider.phieuLinhSuatAn({
          id,
          tongHop: payload.tongHop,
          sapXepTenPhong: payload.sapXepTenPhong,
          dsPhongId: payload.dsPhongId || [],
        });
        break;
      case "P084":
        promise = nbPhieuLinhSuatAnProvider.phieuLinhSuatAn({
          id,
          tongHop: false,
        });
        break;
      case "P668":
        promise = nbPhieuLinhSuatAnProvider.inTemGhiCheDoAn({
          id: id,
          ...payload,
        });
        break;
      case "P083":
        promise = nbDvKhamProvider.inPhieuKskNb({
          nbDotDieuTriId,
          chiDinhTuDichVuId,
          ...payload,
        });
        break;
      case "P092":
        promise = nbDichVuProvider.inPhieuCongKhai({
          nbDotDieuTriId,
          ...payload,
        });
        break;
      case "P094":
        promise = nbDotDieuTriProvider.getPhieuBaoTu(nbDotDieuTriId);
        break;
      case "P103":
        promise = nbDotDieuTriProvider.getBangKeChiPhi10Ngay(nbDotDieuTriId);
        break;
      case "P104":
        promise =
          nbDotDieuTriProvider.getBangKeChiPhiNgoai10Ngay(nbDotDieuTriId);
        break;
      case "P110":
        promise = nbDvKhamProvider.inPhieuKskNhat({ nbThongTinId });
        break;
      case "P112":
      case "P269":
        promise = nbDvCdhaTdcnPtTtProvider.getBangKeChePhi(
          id || dsSoPhieu[0]?.soPhieu
        );
        break;
      case "P114":
        promise = nbDvCdhaTdcnPtTtProvider.phieuPTTT(dsSoPhieu[0]?.soPhieu);
        break;
      case "P128":
        promise = nbDvCdhaTdcnPtTtProvider.temSoKetNoi({
          nbDotDieuTriId,
          hopDongKskId: payload?.hopDongKskId,
        });
        break;
      case "P130":
        promise = nbTiemChungProvider.barcode(nbDotDieuTriId);
        break;
      case "P133":
      case "P325":
        promise = nbDotDieuTriProvider.bangCamKetNoiQuy(nbDotDieuTriId);
        break;
      case "P147":
        promise = nbDotDieuTriProvider.temNguoiBenh(nbDotDieuTriId);
        break;
      case "P160":
        promise = nbDotDieuTriProvider.temTheNuoiBenh(nbDotDieuTriId);
        break;
      case "P159":
        promise = phieuNhapXuatProvider.getTemThuoc({
          phieuNhapXuatId: payload.phieuNhapXuatId,
          loaiNhapXuat: payload.loaiNhapXuat,
        });
        break;
      case "P166":
      case "P1107":
      case "P1290":
        promise = phieuNhapXuatProvider.getPhieuPhatThuoc({
          phieuNhapXuatId: payload.phieuNhapXuatId,
          ...(maPhieu === "P1290" ? { phieuXuatThuoc: true } : {}),
        });
        break;
      case "P169":
        promise = phieuNhapXuatProvider.inPhieuNhapXuat({
          id: payload.phieuNhapXuatId,
        });
        break;
      case "P170":
        promise = phieuNhapXuatProvider.inPhieuLinh({
          id: payload.phieuNhapXuatId,
        });
        break;
      case "P171":
        promise = phieuNhapXuatProvider.inPhieuLinh({
          id: payload.phieuNhapXuatId,
        });
        break;
      case "P176":
      case "P561":
        promise = phieuNhapXuatProvider.getPhieuThuNhaThuoc({
          id: payload.phieuNhapXuatId,
          maViTri: item?.maViTri,
          maPhieuIn: maPhieu,
        });
        break;
      case "P177":
        promise = nbPhieuDoiTraProvider.phieuYeuCauHoanDoi({
          nbDotDieuTriId: nbDotDieuTriId,
          phieuThuId: payload.phieuThuId,
        });
        break;
      case "P294":
      case "P1057":
        promise = pdfUtils.headLessLoad(`/chi-so-song/${nbDotDieuTriId}`);
        break;
      case "P1218":
        promise = pdfUtils.headLessLoad(
          editorUtils.getLinkHeadless({
            ma: maPhieu,
            nbDotDieuTriId,
            mhParams,
            baoCaoId: item.baoCaoId,
          })
        );
        break;
      case "P187":
        promise = nbPhieuTiemChungProvider.inPhieuXuatVacXin({
          nbDotDieuTriId,
          phieuThuId: payload.phieuThuId,
        });
        break;
      case "P193":
        promise = nbDvKyThuatProvider.getPhieuChiDinhTongHop({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          thanhToan: false,
          qrThanhToan
        });
        break;
      case "P194":
        promise = nbPhieuTiemChungProvider.getDonThuoc({
          nbDotDieuTriId,
          dsDonThuocId: (dsSoPhieu || []).map((item) => item?.soPhieu),
          ...(ngonNguParams || {}),
        });
        break;
      case "P198":
      case "P199":
      case "P200":
      case "P201":
      case "P202":
      case "P203":
      case "P204":
        promise = nbDotDieuTriProvider.getPhieuThongTinNb({
          nbDotDieuTriId,
          loai: LOAI_BC_THONG_TIN_NB[maPhieu],
        });
        break;
      case "P205":
        promise = nbBienBanHoiChanProvider.getPhieuTrichBienBanHoiChan({
          id: dsSoPhieu[0]?.soPhieu || payload?.bienBanHoiChanId,
          maBaoCao: item.maBaoCao,
        });
        break;
      case "P549":
        promise = nbBienBanHoiChanProvider.getPhieuTrichBienBanHoiChan({
          // id: dsSoPhieu[0]?.soPhieu || payload?.bienBanHoiChanId,
          id: payload?.bienBanHoiChanId || dsSoPhieu[0]?.soPhieu,
          maBaoCao: item.maBaoCao,
        });
        break;
      case "P206":
        promise = nbBienBanHoiChanProvider.getPhieu({
          id: dsSoPhieu[0]?.soPhieu || payload.bienBanHoiChanId,
          path: GIAY_IN_BIEN_BAN_HOI_CHAN.find(
            (phieu) => phieu.maPhieu === maPhieu
          ).path,
        });
        break;
      case "P207":
        if (item?.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
          if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
            promise = pdfUtils.headLessLoad(
              editorUtils.getLinkHeadless({
                ma: maPhieu,
                soPhieu: item?.dsSoPhieu[0]?.soPhieu,
                nbDotDieuTriId,
                mhParams,
                baoCaoId: item.baoCaoId,
                ...payload,
              })
            );
          }
        } else {
          promise = nbBienBanHoiChanProvider.getPhieu({
            id: dsSoPhieu[0]?.soPhieu || payload.bienBanHoiChanId,
            path: GIAY_IN_BIEN_BAN_HOI_CHAN.find(
              (phieu) => phieu.maPhieu === maPhieu
            ).path,
          });
        }

        break;
      case "P872":
      case "P208":
        // case "P207":
        //Mã P208 dùng chung cho cả editor và form word
        //trong mh hsba ==> check loại biểu mẫu để có thể call headless hoặc call api get phiếu
        if (item?.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
          if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
            promise = pdfUtils.headLessLoad(
              editorUtils.getLinkHeadless({
                ma: maPhieu,
                soPhieu: item?.dsSoPhieu[0]?.soPhieu,
                nbDotDieuTriId,
                mhParams,
                baoCaoId: item.baoCaoId,
                ...payload,
              })
            );
          }
        } else {
          promise = nbBienBanHoiChanProvider.getPhieuBienBanHoiChan({
            id: dsSoPhieu[0]?.soPhieu || payload?.bienBanHoiChanId,
            loaiBieuMau: 10,
          });
        }

        break;
      case "P209":
      case "P255":
        promise = nbDotDieuTriProvider.getPhieuBaoTu(nbDotDieuTriId);
        break;
      case "P210":
      case "P726":
        if (item?.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
          if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
            promise = pdfUtils.headLessLoad(
              editorUtils.getLinkHeadless({
                ma: maPhieu,
                soPhieu: item?.dsSoPhieu[0]?.soPhieu,
                nbDotDieuTriId,
                mhParams,
                baoCaoId: item.baoCaoId,
                ...payload,
              })
            );
          }
        } else {
          promise = nbDvKhamProvider.getPhieuChiDinhKetLuan({
            id: chiDinhTuDichVuId,
            nbDotDieuTriId,
          });
        }
        break;
      case "P211":
        promise = nbDvKhamProvider.getPhieuChiDinhKetLuan({
          id: chiDinhTuDichVuId,
          nbDotDieuTriId,
        });
        break;
      case "P212":
        if (item?.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
          if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
            promise = pdfUtils.headLessLoad(
              editorUtils.getLinkHeadless({
                ma: maPhieu,
                soPhieu: item?.dsSoPhieu[0]?.soPhieu,
                nbDotDieuTriId,
                mhParams,
                baoCaoId: item.baoCaoId,
                ...payload,
              })
            );
          }
        } else {
          promise = nbDvKhamProvider.getPhieuChiDinhKetLuan({
            id: chiDinhTuDichVuId || dsSoPhieu[0]?.ten2,
            nbDotDieuTriId,
          });
        }
        break;
      case "P214":
        promise = nbPhieuSoKetProvider.phieuSoKet(dsSoPhieu[0]?.soPhieu);
        break;
      case "P216":
        promise = nbDvChePhamMauProvider.getPhieuPhatDuTruMau({
          phieuNhapXuatId: payload?.phieuNhapXuatId,
          nbDotDieuTriId,
          // dichVuId: dsSoPhieu.length > 1 ? null : dsSoPhieu[0]?.soPhieu,
        });
        break;
      case "P267":
        promise = nbDvChePhamMauProvider.getPhieuPhatDuTruMau({
          phieuNhapXuatId: dsSoPhieu[0]?.soPhieu,
          nbDotDieuTriId,
        });
        break;

      case "P224":
      case "P225":
      case "P226":
        promise = nbKhamTiemChungProvider.inPhieuTiemChung({
          id,
          phongId: payload?.phongId,
          nbDvKhamId: chiDinhTuDichVuId,
        });
        break;
      case "P246":
        promise = nbDvChePhamMauProvider.getPhieuXNPhatMau({
          phieuNhapXuatId: payload?.phieuNhapXuatId,
          nbDotDieuTriId,
        });
        break;
      case "P262":
        promise = nbDvThuocChiDinhNgoaiProvider.getDonChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          dsDonThuocId: dsSoPhieu[0]?.soPhieu,
        });
        break;
      case "P264":
        promise = nbDvKhamProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          dsPhieuInId: dsSoPhieu[0]?.soPhieu,
          ...(loai && { loai }),
        });
        break;
      case "P278":
        promise = nbDvVatTuProvider.inDonVatTu({
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          donThuocId: dsSoPhieu[0]?.soPhieu,
        });
        break;
      case "P287":
      case "P288":
        promise = nbThanhToanProvider.inPhieuHuongDanThanhToanQr({
          thanhToanId: thanhToanId || dsSoPhieu[0]?.soPhieu,
        });
        break;
      case "P1151":
        promise = nbThanhToanProvider.inPhieuHuongDanThanhToanQr({
          thanhToanId: thanhToanId || dsSoPhieu[0]?.soPhieu,
          maViTri: payload?.maViTri,
          maPhieuIn: payload?.maPhieuIn,
        });
        break;
      case "P300":
      case "P308":
      case "P309":
      case "P358":
      case "P477":
      case "P546":
      case "P552":
      case "P624":
      case "P639":
      case "P663":
      case "P785":
      case "P786":
      case "P787":
        promise = nbSangLocSuyDdProvider.inPhieuSangLocDd(
          dsSoPhieu[0]?.soPhieu
        );
        break;
      case "P304":
        promise = nbPhieuDoiTraProvider.phieuChi({ id });
        break;
      case "P1041":
        promise = nbPhieuDoiTraProvider.phieuChi({ id, loai: 50 });
        break;
      case "P1042":
        promise = nbPhieuDoiTraProvider.phieuChi({
          id,
          loai: 50,
          maPhieuIn: maPhieu,
        });
        break;
      case "P318_1":
        promise = nbDvKhamProvider.xuatFileSoKhamBenh({
          nbDotDieuTriId,
          ...payload,
        });
        break;
      case "P318_2":
        promise = nbDotDieuTriProvider.temNbKSK({
          nbDotDieuTriId,
          ...payload,
        });
        break;
      case "P359":
        if (payload.inThuong) {
          promise = nbTamUngProvider.phieuBienNhanHoanTienMat({
            phieuThuId: payload.phieuThuId,
          });
        } else {
          promise = nbTamUngProvider.phieuBienNhanHoanTienMatById(payload.id);
        }
        break;
      case "P330":
      case "P331":
      case "P332":
      case "P333":
      case "P334":
      case "P335":
      case "P336":
      case "P337":
      case "P338":
      case "P339":
      case "P340":
      case "P341":
      case "P342":
      case "P343":
      case "P344":
      case "P345":
      case "P346":
      case "P347":
      case "P022":
        //với mã phiếu kq xn (P022, P330 -> P347) : FE check nếu có data mục dsDuongDan  thì lấy file từ đó luôn, k cần gọi api phiếu kq
        //riêng mã P022 → nếu dsDuongDan  null thì mới gọi api kết quả
        if (maPhieu === "P022" && !dsSoPhieu[0]?.dsDuongDan) {
          promise = new Promise((resolve, reject) => {
            nbDvXetNghiemProvider
              .getPhieuKetQua({
                nbDotDieuTriId,
                baoCaoId: item.baoCaoId,
                dsSoPhieuId: dsSoPhieu[0]?.soPhieu,
              })
              .then((s) => {
                let _data =
                  maPhieu === "P022"
                    ? s.data
                    : { dsPhieuLis: s.data?.dsPhieuLis || [] };
                s.data = locPhieuLisPacs(_data, {
                  allData: true,
                  isLocPhieu: true,
                  isXetNghiem: true,
                  isAllHisLisPacs: true,
                });
                resolve(s);
              })
              .catch((e) => {
                resolve({
                  code: 1,
                });
              });
          });
        } else {
          promise = new Promise((resolve, reject) => {
            const dsPdf = flatten(item.dsSoPhieu.map((x) => x.dsDuongDan));

            resolve({
              data: {
                ...item,
                file: {
                  pdf: dsPdf,
                },
              },
            });
          });
        }
        break;
      case "P348":
      case "P349":
      case "P350":
      case "P351":
      case "P352":
      case "P353":
      case "P354":
      case "P355":
      case "P023":
        promise = new Promise((resolve, reject) => {
          nbDvCdhaTdcnPtTtProvider
            .getPhieuKetQua({
              nbDotDieuTriId,
              baoCaoId: item.baoCaoId,
              dsSoKetNoi: dsSoPhieu[0]?.soPhieu,
            })
            .then((s) => {
              let _data =
                maPhieu === "P023"
                  ? s.data
                  : { dsPhieuPacs: s.data?.dsPhieuPacs || [] };
              s.data = locPhieuLisPacs(_data, {
                allData: true,
                isLocPhieu: true,
                isCdha: true,
                isAllHisLisPacs: true,
              });
              resolve(s);
            })
            .catch((e) => {
              resolve({
                code: 1,
              });
            });
        });
        break;
      case "P360":
        promise = nbPhieuThuProvider.getInPhieuThu({
          phieuThuId: payload.phieuThuId,
        });
        break;
      case "P362":
        promise = nbTuVanThuocProvider.getPhieuTuVanThuocDuocSi(id);
        break;
      case "P363":
        promise = nbTuVanThuocProvider.getPhieuTuVan(id);
        break;
      case "P365":
        promise = nbLuongGiaPHCNProvider.getPhieuLuongGia(
          id || dsSoPhieu[0]?.soPhieu
        );
        break;
      case "P367":
        promise = new Promise(async (resolve, reject) => {
          try {
            //gọi api sinh qr trước
            await nbThanhToanProvider.sinhQrPhieuThu(nbDotDieuTriId);

            //gọi api get phiếu sau
            const data = await nbThanhToanProvider.phieuHuongDanQrNb(
              nbDotDieuTriId
            );

            resolve(data);
          } catch (error) {
            reject(error);
          }
        });
        break;
      case "P371":
      case "P374":
        promise = nbDotDieuTriProvider.getGiayCamKetDieuTriLao(nbDotDieuTriId);
        break;
      case "P392":
        promise = nbHenNoiSoiProvider.getGiayHen(id);
        break;
      case "P394":
        promise =
          nbPhucHoiCnKhamProvider.getPhieuKhamChiDinhPhcn(chiDinhTuDichVuId);
        break;
      case "P400":
      case "P402":
        promise = nbDotDieuTriProvider.donXinDieuTriTheoYC(nbDotDieuTriId);
        break;
      case "P430":
      case "P431":
        promise = nbDanhSachLichHenProvider.getPhieuHenCMU(nbDotDieuTriId);
        break;
      case "P468":
        promise = nbDvChePhamMauProvider.inPhieuLinhMau({
          nbDotDieuTriId,
          phieuNhapXuatId: payload?.phieuNhapXuatId,
        });
        break;
      case "P485":
        promise = nbDvKhamProvider.inPhieuDonKinh({
          nbDvKhamId: dsSoPhieu[0]?.soPhieu,
        });
        break;
      case "P895":
        promise = nbPhieuThuProvider.getInPhieuThu({
          phieuThuId: item.soPhieu,
          maPhieuIn: payload.maPhieuIn,
          maViTri: payload.maViTri,
        });
        break;
      case "P555":
      case "P999":
        promise = nbPhieuThuProvider.getInPhieuThu({
          phieuThuId: payload.phieuThuId,
          maPhieuIn: payload.maPhieuIn,
          maViTri: payload.maViTri,
          baoCaoId: payload.baoCaoId,
        });
        break;
      case "P149":
      case "P617":
        if (item?.dsSoPhieu && item?.dsSoPhieu[0]?.soPhieu) {
          promise = pdfUtils.headLessLoad(
            editorUtils.getLinkHeadless({
              ma: maPhieu,
              soPhieu: item?.dsSoPhieu[0]?.soPhieu,
              nbDotDieuTriId,
              nbThongTinId,
              mhParams,
              tuThoiGian: item?.dsSoPhieu[0]?.tuThoiGian,
              denThoiGian: item?.dsSoPhieu[0]?.denThoiGian,
              baoCaoId: item.baoCaoId,
              ...payload,
            })
          );
        }
        break;
      case "P132":
      case "P651":
        promise = nbDotDieuTriProvider.camDoanSuDungDv(nbDotDieuTriId, {
          maPhieuIn: maPhieu,
        });
        break;
      case "P629":
        promise = nbDvNgoaiDieuTriProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu:
            dsChiDinhTuLoaiDichVu || dsSoPhieu[0]?.chiDinhTuLoaiDichVu,
          ...(payload?.maViTri == "00607"
            ? {}
            : {
                chiDinhTuDichVuId:
                  chiDinhTuDichVuId || dsSoPhieu[0]?.chiDinhTuDichVuId,
              }),
          dsNbDichVuId: payload.dsNbDichVuId || dsSoPhieu[0].soPhieu,
        });
        break;
      case "P656":
        promise = nbKhamCkMatProvider.getPhieuKetQuaKhamKhucXa(
          payload.dvKhamId || dsSoPhieu[0].soPhieu
        );
        break;
      case "P655":
        promise = nbKhamCkMatProvider.getPhieuKetQuaDoThiLuc(
          payload.dvKhamId || dsSoPhieu[0].soPhieu
        );
        break;
      case "P652":
        promise = nbPhieuTiemChungProvider.getPhieuTiemChung({
          nbDotDieuTriId,
        });
        break;
      case "P675":
        promise = nbDvKhamProvider.inGiayKskNb({
          id: dsSoPhieu[0].soPhieu,
          formA4: false,
        });
        break;
      case "P707":
        promise = nbPhieuThuProvider.getInBienLaiThuPhi(payload.phieuThuId);
        break;
      case "P728":
        const dsSoPhieuId = (dsSoPhieu || []).map((x) => x.soPhieu);
        promise = nbDvXetNghiemProvider.getPhieuChiDinh({
          nbDotDieuTriId,
          phieuChiDinhId: item.baoCaoId,
          dsSoPhieuId: uniq(dsSoPhieuId),
        });
        break;
      case "CHUNGNHAN":
        if (payload.nbDvKhamId) {
          promise = nbDvKhamProvider.getPhieuTongHop({
            nbDvKhamId: payload.nbDvKhamId,
            baoCaoId: item.baoCaoId,
          });
        }
        break;
      case "P1035":
        promise = nbDvKhamProvider.getPhieuTongHop({
          nbDvKhamId: chiDinhTuDichVuId,
          baoCaoId: item.baoCaoId,
        });
        break;
      case "P775":
        promise = nbPhieuThuProvider.getInPhieuMienGiam(payload.phieuThuId);
        break;
      case "P881":
        promise = nbDotDieuTriProvider.getInPhieuThanToanRaVien(nbDotDieuTriId);
        break;
      case "P876":
      case "P949":
        promise = nbDotDieuTriProvider.getBangKeChiPhiNgoaiTru({
          nbDotDieuTriId,
          loai: LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_CHI_PHI_DV_NGOAI_TRU,
          ...(maPhieu === "P876" ? { thanhToan: 0 } : {}),
        });
        break;
      case "P305":
        promise = nbPhaCheProvider.getPhieuPhaChe({
          dsId: id ?? dsSoPhieu[0].soPhieu,
        });
        break;
      case "P306":
        promise = nbPhaCheProvider.getPhieuDichPha({
          dsId: id ?? dsSoPhieu[0].soPhieu,
        });
        break;
      case "P307":
        promise = nbPhaCheProvider.getNhanPcHangLoat({
          dsId: id ?? dsSoPhieu[0].soPhieu,
          lien: "1",
        });
        break;
      case "P918":
      case "P919":
        promise = nbPhaCheThuocProvider.getNhanPhaCheThuoc({
          dsId: id ?? dsSoPhieu[0].soPhieu,
        });
        break;
      case "P1149":
        promise = nbPhaCheThuocProvider.getNhanPhaCheThuoc({
          dsId: id ?? dsSoPhieu[0].soPhieu,
          temGoc: true,
        });
        break;
      case "P997":
        promise = nbPhaCheThuocProvider.getPhieuCongKhaiPhaCheThuoc(
          id ?? dsSoPhieu[0].soPhieu
        );
        break;
      case "P1002":
        promise = nbPhaCheThuocProvider.getPhieuPhaCheThuoc(
          id ?? dsSoPhieu[0].soPhieu
        );
        break;
      case "P1096":
        promise = nbChuyenKhoaProvider.getPhieuDatGiuong({
          nbDotDieuTriId,
          nbChuyenKhoaId: payload?.nbChuyenKhoaId,
          dsTrangThaiHoan: payload?.dsTrangThaiHoan,
          maViTri: payload?.maViTri,
          maPhieuIn: payload?.maPhieuIn,
        });
        break;
      case "P1046":
        promise = dichVuProvider.temNguoiBenh(nbDotDieuTriId, {
          conThu: dsSoPhieu[0]?.soPhieu,
        });
        break;
      case "P1137":
      case "P1138":
        promise = nbDvKhamProvider.getPhieuKetQuaCLS({
          nbDotDieuTriId,
        });
        break;
      case "P1154":
      case "P1180":
        promise = nbDvThuocProvider.getDonChiDinh({
          nbDotDieuTriId,
          chiDinhTuDichVuId: dsSoPhieu[0].soPhieu,
          thuocThuong: dsSoPhieu[0]?.thuocThuong,
          ...(maPhieu === "P1154"
            ? { dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM }
            : {}),
        });
        break;

      case "P1202":
        promise = nbDvCdhaTdcnPtTtProvider.getPhieuHenTraKQCls(
          id ?? dsSoPhieu[0].soPhieu
        );
        break;
      case "P1214":
      case "P1215":
        promise = nbCapPhatThuocProvider.inNhanThuoc({
          nbDotDieuTriId,
          dungKem: payload?.dungKem,
          maViTri: payload?.maViTri,
          maPhieuIn: payload?.maPhieuIn,
          dsThuoc: payload.dsThuoc,
        });
        break;
      case "P1242":
        promise = nbDvKyThuatProvider.getPhieuChiDinhTongHop({
          nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId,
          dsNhomDichVuCap2Id: payload?.dsNhomDichVuCap2Id,
          phcn: true,
        });
        break;
      case "P1182":
        promise = nbPhaCheThuocChiTietProvider.getBienBanHuyThuoc({
          tuThoiGian: payload?.tuThoiGian
            ? moment(payload?.tuThoiGian).format("YYYY-MM-DD 00:00:00")
            : null,
          denThoiGian: payload?.denThoiGian
            ? moment(payload?.denThoiGian).format("YYYY-MM-DD 23:59:59")
            : null,
          dsKhoId: payload.dsKhoId,
          dsDichVuId: payload.dsDichVuId,
        });
        break;
      case "P1303":
        promise = goiSoProvider.inPhieuSTTPhatThuoc({
          nbDotDieuTriId,
          ...payload,
        });
        break;
      default:
        if (item.loaiBieuMau == LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
          promise = pdfUtils.headLessLoad(item.linkEditor);
        } else {
          //SAKURA-58852: Nếu không có thiết lập phiếu
          // => tự động ghép api phiếu in sau khi khai báo đủ ở danh mục báo cáo, danh mục phiếu in
          // (check trong api phieu-in BE trả về các mã báo cáo, mã phiếu in, vị trí) và mặc định param in theo nbDotDieuTriId đang thao tác
          promise = nbDotDieuTriProvider.getPhieuTongHop({
            nbDotDieuTriId,
            baoCaoId: item.baoCaoId,
          });
          // console.log(t("phieuIn.chuaDuocCauHinhThongTinPhieu"), item);
          // if (showError) {
          //   message.error(t("phieuIn.chuaDuocCauHinhThongTinPhieu"));
          // }
        }
        break;
    }
  }
  if (promise) {
    return new Promise((resolve, reject) => {
      promise
        .then((phieu) => {
          if (!phieu.data || (isArray(phieu.data) && !phieu.data?.length)) {
            throw Error(phieu.message || t("common.khongCoDuLieu"));
          }

          //kiểm tra trường loại biểu mẫu = phiếu scan (loaiBieuMau = 60)
          //=> khi in phiếu: lấy file theo ds đường dẫn ở từng object: dsSoPhieu.dsDuongDan
          if (Array.isArray(phieu.data)) {
            phieu.data = phieu.data.map((item) => {
              //nếu phiếu không có field file thì tạo field file
              if (!item?.file?.pdf && item?.dsDuongDan?.[0])
                return {
                  ...item,
                  file: { pdf: item.dsDuongDan?.[0] },
                };
              return item;
            });
          }
          let item = phieu.data;
          let data = phieu.data;
          if (Array.isArray(data)) {
            item = item.filter((item) => item);
            data = data.filter((item) => item);
            if (!data.length) {
              //SAKURA-11314 FE [Khám bệnh - in phiếu hoàn đổi] Lỗi: không hiển thị thông báo khi không có phiếu hoàn đổi
              if (maPhieu == "P052") {
                throw {
                  message: t("phieuIn.khongTonTaiPhieuYeuCauHoanDoi"),
                };
              }
              if (maPhieu == "P005") {
                throw {
                  message: t("phieuIn.khongTonTaiPhieuIn"),
                };
              }
              resolve(null);
              return;
            }
          }
          if (Array.isArray(item?.file?.pdf)) {
            item.filePdf = item?.file?.pdf
              ?.map((x) => x)
              .filter((item) => item);
          } else {
            if (Array.isArray(item)) {
              item = { data: cloneDeep(item) };
              item.filePdf = item.data.map((item) => item.file.pdf);
            } else item.filePdf = item?.file?.pdf ? [item?.file?.pdf] : [];
          }
          item.type = phieu?.type;

          resolve(item);
        })
        .catch((e) => {
          if (showError)
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          resolve({ data: e, code: 1 });
        });
    });
  } else {
    return new Promise((resolve, reject) => {
      resolve({
        data: { message: t("phieuIn.chuaDuocCauHinhThongTinPhieu") },
        code: 1,
      });
    });
  }
};
