import React from "react";
import moment from "moment";
import { Dropdown, Input, Menu } from "antd";
import { checkRole, checkRoleOr } from "lib-utils/role-utils";
import { ROLES } from "constants/index";
import { Select, HeaderSearch, Tooltip } from "components";
import { SVG } from "assets";

export const columns = ({
  onClickSort,
  onSettings,
  dataSortColumn,
  onSearchInput,
  t,
  onInPhieuTamUng,
  listdoiTuongKcb,
  onInPhieuBienNhanHoanTien,
  onEditHoanTamUng,
  handleDeletePhieuHoanTamUng,
  listAllQuayTiepDon,
  listAllToaNha,
}) => {
  const menu = (data) => (
    <Menu
      items={[
        ...(checkRoleOr([ROLES["THU_NGAN"].IN_PHIEU_HOAN_TAM_UNG])
          ? [
              {
                key: 1,
                label: (
                  <a onClick={() => onInPhieuTamUng(data.id)}>
                    <div>
                      <span>{t("thuNgan.quanLyTamUng.phieuHoanTamUng")}</span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(data.tienConLai > 0
          ? [
              {
                key: 2,
                label: (
                  <a onClick={() => onInPhieuBienNhanHoanTien(data.id)}>
                    <div>
                      <span>{t("thuNgan.phieuBienNhanHoanTien")}</span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
      ]}
    />
  );

  return [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.tienTraLaiNB")}
          sort_key="tienConLai"
          dataSort={dataSortColumn["tienConLai"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "tienConLai",
      key: "tienConLai",
      columnName: t("thuNgan.quanLyTamUng.tienTraLaiNB"),
      show: true,
      align: "right",
      render: (field, item, index) => {
        return <div>{field && field > 0 && field?.formatPrice()}</div>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.soPhieuThuTamUng")}
          sort_key="soPhieuTamUng"
          dataSort={dataSortColumn["soPhieuTamUng"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "soPhieuTamUng",
      key: "soPhieuTamUng",
      columnName: t("thuNgan.quanLyTamUng.soPhieuThuTamUng"),
      show: true,
      align: "right",
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.soTienHoan")}
          sort_key="tongTien"
          dataSort={dataSortColumn["tongTien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "tongTien",
      key: "tongTien",
      columnName: t("thuNgan.quanLyTamUng.soTienHoan"),
      show: true,
      align: "right",
      render: (field, item, index) => {
        return <div>{field && field?.formatPrice()}</div>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.soPhieuHoan")}
          sort_key="soPhieu"
          dataSort={dataSortColumn["soPhieu"] || ""}
          onClickSort={onClickSort}
          search={
            <Input
              placeholder={t("thuNgan.quanLyTamUng.nhapSoPhieuHoan")}
              onChange={onSearchInput("soPhieu")}
            />
          }
        />
      ),
      width: "100px",
      dataIndex: "soPhieu",
      key: "soPhieu",
      align: "left",
      columnName: t("thuNgan.quanLyTamUng.soPhieuHoan"),
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.kyHieu")}
          sort_key="kyHieu"
          dataSort={dataSortColumn["kyHieu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "kyHieu",
      key: "kyHieu",
      align: "left",
      columnName: t("thuNgan.quanLyTamUng.kyHieu"),
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.lyDoHoanTamUng")}
          sort_key="tenLyDoTamUng"
          dataSort={dataSortColumn["tenLyDoTamUng"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "tenLyDoTamUng",
      key: "tenLyDoTamUng",
      align: "left",
      columnName: t("thuNgan.quanLyTamUng.lyDoHoanTamUng"),
      show: true,
      render: (field, item, index) => {
        return <div>{field}</div>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.ngayPhieuHoan")}
          sort_key="thoiGianThucHien"
          dataSort={dataSortColumn["thoiGianThucHien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      columnName: t("thuNgan.quanLyTamUng.ngayPhieuHoan"),
      show: true,
      render: (field, item, index) => {
        return field && moment(field).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.khoa")}
          sort_key="tenKhoa"
          dataSort={dataSortColumn["tenKhoa"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "tenKhoa",
      key: "tenKhoa",
      columnName: t("common.khoa"),
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.thuNgan")}
          sort_key="tenThuNgan"
          dataSort={dataSortColumn["tenThuNgan"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "80px",
      dataIndex: "tenThuNgan",
      key: "tenThuNgan",
      align: "left",
      columnName: t("thuNgan.thuNgan"),
      show: true,
      render: (field, item, index) => {
        return field;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.ghiChu")}
          sort_key="ghiChu"
          dataSort={dataSortColumn["ghiChu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "ghiChu",
      key: "ghiChu",
      align: "left",
      columnName: t("common.ghiChu"),
      show: true,
      render: (field, item, index) => {
        return field;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.soPhieuThu")}
          sort_key="soPhieu"
          dataSort={dataSortColumn["soPhieu"] || ""}
          onClickSort={onClickSort}
          search={
            <Input
              placeholder={t("thuNgan.quanLyTamUng.nhapSoPhieuThu")}
              //   onChange={onSearchInput("")}
            />
          }
        />
      ),
      width: "80px",
      dataIndex: "",
      key: "",
      align: "left",
      columnName: t("thuNgan.soPhieuThu"),
      show: true,
      render: (field, item, index) => {
        return field;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.doiTuongKCB")}
          sort_key="doiTuongKcb"
          dataSort={dataSortColumn["doiTuongKcb"] || ""}
          onClickSort={onClickSort}
          searchSelect={
            <Select
              placeholder={t("thuNgan.doiTuongKCB")}
              onChange={onSearchInput("doiTuongKcb")}
              data={listdoiTuongKcb}
            />
          }
        />
      ),
      width: "100px",
      dataIndex: "doiTuongKcb",
      key: "doiTuongKcb",
      align: "left",
      columnName: t("thuNgan.doiTuongKCB"),
      show: true,
      render: (item, index) => {
        return listdoiTuongKcb.find((x) => x.id === item)?.ten || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("baoCao.quayThu")}
          sort_key="quayId"
          dataSort={dataSortColumn["quayId"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "quayId",
      key: "quayId",
      columnName: t("baoCao.quayThu"),
      show: true,
      render: (item) => {
        return (listAllQuayTiepDon || []).find((x) => x.id === item)?.ten || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.nhaThu")}
          sort_key="nhaTamUngId"
          dataSort={dataSortColumn["nhaTamUngId"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "nhaTamUngId",
      key: "nhaTamUngId",
      columnName: t("thuNgan.nhaThu"),
      show: true,
      render: (item) => {
        return (listAllToaNha || []).find((x) => x.id === item)?.ten || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.thaoTac")}
              <SVG.IcSetting onClick={onSettings} className="icon" />
            </>
          }
        />
      ),
      width: "100px",
      dataIndex: "",
      key: "",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (item, data) => {
        return (
          <>
            {checkRole([ROLES["THU_NGAN"].XOA_PHIEU_HOAN_TAM_UNG]) && (
              <Tooltip title={t("thuNgan.xoaPhieuHoanTamUng")}>
                <SVG.IcDelete
                  className="ic-action"
                  onClick={() => handleDeletePhieuHoanTamUng(item)}
                />
              </Tooltip>
            )}
            {checkRole([ROLES["THU_NGAN"].SUA_PHIEU_HOAN_TAM_UNG]) && (
              <Tooltip title={t("thuNgan.suaPhieuHoanTamUng")}>
                <SVG.IcEdit
                  className="ic-action"
                  onClick={() => onEditHoanTamUng(item)}
                />
              </Tooltip>
            )}
            <Dropdown
              overlay={menu(data)}
              trigger={["click"]}
              placement="topCenter"
            >
              <SVG.IcPrint className="ic-action" />
            </Dropdown>
          </>
        );
      },
    },
  ];
};
