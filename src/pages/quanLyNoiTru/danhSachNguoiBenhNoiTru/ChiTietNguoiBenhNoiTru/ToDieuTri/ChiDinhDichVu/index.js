import React, { useEffect, useMemo, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>th<PERSON>rapper } from "components";
import DonThuoc from "./DonThuoc";
import { Main } from "./styled";
import DichVuKyThuat from "./DichVuKyThuat";
import { useDispatch } from "react-redux";
import SuatAn from "./SuatAn";
import { CaretDownOutlined } from "@ant-design/icons";
import GoiPTTT from "./GoiPTTT";
import HoaChat from "./HoaChat";
import PhacDoDieuTri from "./PhacDoDieuTri";
import {
  DOI_TUONG_KCB,
  LOAI_DICH_VU,
  ROLES,
  THIET_LAP_CHUNG,
  LOAI_CHE_DO_AN,
} from "constants/index";
import { setQueryStringValue } from "hooks/useQueryString/queryString";
import { useQueryString, useStore, useThietLap } from "hooks";
import Mau from "./Mau";
import { SVG } from "assets";
import ChiDinhVatTu from "./ChiDinhVatTu";
import { checkRole } from "lib-utils/role-utils";
import { useTranslation } from "react-i18next";
import ChePhamDinhDuong from "./ChePhamDinhDuong";
import ThuocVatTu from "./ThuocVatTu";
import PhaCheThuocTheoDon from "./PhaCheThuocTheoDon";
import BoChiDinh from "./BoChiDinh";

const ChiDinhDichVu = (props) => {
  const { t } = useTranslation();
  const [tab] = useQueryString("tab", "0");
  const refPhaCheThuocTheoDon = useRef(null);
  const refBoChiDinh = useRef(null);

  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const [state, _setState] = useState({
    activeKey: "0",
  });
  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const [dataTAB_VAT_TU_THEO_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TAB_VAT_TU_THEO_THUOC
  );

  const currentToDieuTri = useStore("toDieuTri.currentToDieuTri");
  const {
    thietLapChonKho: { getListThietLapChonKhoTheoTaiKhoan },
    chiDinhKhamBenh: { updateConfigData },
  } = useDispatch();
  const { isReadonly, layerId, checkChongChiDinh } = props;
  const onChangeTab = (e) => {
    // debugger
    setQueryStringValue("tab", e);
    setState({ activeKey: e });

    let loaiDichVu = LOAI_DICH_VU.THUOC;
    if (e === "3" || e === "10") {
      loaiDichVu = LOAI_DICH_VU.VAT_TU;
    }
    if (e === "5") {
      loaiDichVu = LOAI_DICH_VU.CHE_PHAM_MAU;
    }
    if (e === "6") {
      loaiDichVu = LOAI_DICH_VU.HOA_CHAT;
    }
    if (e === "8") {
      loaiDichVu = LOAI_DICH_VU.CHE_PHAM_DINH_DUONG;
    }

    let payload = {
      khoaNbId: chiTietNguoiBenhNoiTru?.khoaNbId,
      khoaChiDinhId: currentToDieuTri?.khoaChiDinhId,
      doiTuong: chiTietNguoiBenhNoiTru?.doiTuong,
      loaiDoiTuongId: chiTietNguoiBenhNoiTru?.loaiDoiTuongId,
      capCuu: chiTietNguoiBenhNoiTru?.capCuu,
      phongId: chiTietNguoiBenhNoiTru?.phongId,
      noiTru: true,
      canLamSang: false,
      loaiDichVu: loaiDichVu,
    };

    if (e === "9") {
      getListThietLapChonKhoTheoTaiKhoan({
        ...payload,
        loaiDichVu: LOAI_DICH_VU.THUOC,
      });
      getListThietLapChonKhoTheoTaiKhoan({
        ...payload,
        loaiDichVu: LOAI_DICH_VU.VAT_TU,
      });
    } else {
      getListThietLapChonKhoTheoTaiKhoan({ ...payload });
    }

    if (e === "12") {
      refBoChiDinh.current && refBoChiDinh.current.refresh();
    }
  };

  useEffect(() => {
    if (
      chiTietNguoiBenhNoiTru.id == currentToDieuTri.nbDotDieuTriId &&
      currentToDieuTri.id
    ) {
      let maBenhId =
        currentToDieuTri?.dsCdChinhId &&
        currentToDieuTri?.dsCdChinhId.length > 0
          ? currentToDieuTri?.dsCdChinhId[0]
          : null;

      updateConfigData({
        configData: {
          chiDinhTuDichVuId: currentToDieuTri.id,
          dsChiDinhTuDichVuId: currentToDieuTri.id,
          thoiGianThucHien: currentToDieuTri.thoiGianYLenh,
          nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
          nbThongTinId: chiTietNguoiBenhNoiTru.nbThongTinId,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          khoaChiDinhId: currentToDieuTri.khoaChiDinhId,
          thongTinNguoiBenh: chiTietNguoiBenhNoiTru,
          isPhauThuat: currentToDieuTri.phauThuat,
          trangThaiKham: "",
          phongThucHienId: chiTietNguoiBenhNoiTru.phongId,
          doiTuongKcb: chiTietNguoiBenhNoiTru.doiTuongKcb,
          maBenhId,
          isNoiTru: true,
          canLamSang: false,
          thoiGianYLenh: currentToDieuTri.thoiGianYLenh,
        },
      });
    }
  }, [currentToDieuTri, chiTietNguoiBenhNoiTru]);

  useEffect(() => {
    if (chiTietNguoiBenhNoiTru?.id) {
      onChangeTab(tab);
      return () => {
        updateConfigData({
          configData: null,
        });
      };
    }
  }, [chiTietNguoiBenhNoiTru?.id]);

  const checkLoaiCheDoAn = useMemo(() => {
    const loai = currentToDieuTri?.loaiCheDoAn;
    return [
      LOAI_CHE_DO_AN.TAM_NHIN_AN,
      LOAI_CHE_DO_AN.TU_TUC,
      LOAI_CHE_DO_AN.DIEU_TRI_KET_HOP,
      LOAI_CHE_DO_AN.DUY_TRI_SU_DUNG,
    ].includes(loai);
  }, [currentToDieuTri?.loaiCheDoAn]);

  const listTabs = [
    {
      name: t("common.chiDinhDichVu"),
      component: (
        <DichVuKyThuat
          isReadonly={isReadonly}
          layerId={state.activeKey === "0" ? layerId : null}
          checkChongChiDinh={checkChongChiDinh}
        />
      ),
      iconTab: <SVG.IcChiDinhDichVu />,
      key: "0",
    },
    {
      name: t("quanLyNoiTru.goiMo10Ngay"),
      component: (
        <GoiPTTT
          isReadonly={isReadonly}
          layerId={state.activeKey === "1" ? layerId : null}
          checkChongChiDinh={checkChongChiDinh}
        />
      ),
      iconTab: <SVG.IcThongTinPttt />,
      roles: [ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_TAB_GOI_MO_10_NGAY],
      key: "1",
    },
    {
      name: t("common.thuoc"),
      component: (
        <DonThuoc
          isReadonly={isReadonly}
          layerId={state.activeKey === "2" ? layerId : null}
          checkChongChiDinh={checkChongChiDinh}
        />
      ),
      iconTab: <SVG.IcDonThuoc color={"var(--color-gray-primary)"} />,
      key: "2",
    },
    {
      name: t("common.vatTu"),
      component: (
        <ChiDinhVatTu
          isReadonly={isReadonly}
          layerId={state.activeKey === "3" ? layerId : null}
          checkChongChiDinh={checkChongChiDinh}
        />
      ),
      iconTab: <SVG.IcVatTu />,
      key: "3",
    },
    {
      name: t("common.suatAn"),
      component: (
        <SuatAn
          isReadonly={isReadonly}
          layerId={state.activeKey === "4" ? layerId : null}
          checkChongChiDinh={checkChongChiDinh}
          checkLoaiCheDoAn={checkLoaiCheDoAn}
        />
      ),
      iconTab: <SVG.IcSuatAn />,
      key: "4",
    },
    {
      name: t("quanLyNoiTru.dvNoiTru.mau"),
      component: (
        <Mau
          isReadonly={isReadonly}
          layerId={state.activeKey === "5" ? layerId : null}
          checkChongChiDinh={checkChongChiDinh}
        />
      ),
      iconTab: <SVG.IcMau />,
      key: "5",
    },
    {
      name: t("danhMuc.hoaChat"),
      iconTab: <SVG.IcHoaChat />,
      component: (
        <HoaChat
          isReadonly={isReadonly}
          layerId={state.activeKey === "6" ? layerId : null}
          checkChongChiDinh={checkChongChiDinh}
        />
      ),
      key: "6",
    },
    {
      name: t("title.phacDoDieuTri"),
      iconTab: <SVG.IcPhacDoDieuTri2 />,
      component: (
        <PhacDoDieuTri
          isReadonly={isReadonly}
          checkChongChiDinh={checkChongChiDinh}
        />
      ),
      key: "7",
    },
    {
      name: t("quanLyNoiTru.dvNoiTru.chePhamDinhDuong"),
      iconTab: <SVG.IcChePhamDD />,
      component: (
        <ChePhamDinhDuong
          isReadonly={isReadonly}
          layerId={state.activeKey === "8" ? layerId : null}
          checkChongChiDinh={checkChongChiDinh}
          checkLoaiCheDoAn={checkLoaiCheDoAn}
        />
      ),
      key: "8",
    },
    ...(dataTAB_VAT_TU_THEO_THUOC &&
    dataTAB_VAT_TU_THEO_THUOC.toLowerCase() == "true"
      ? [
          {
            name: t("common.thuocVatTu"),
            component: (
              <ThuocVatTu
                isReadonly={isReadonly}
                layerId={state.activeKey === "9" ? layerId : null}
                checkChongChiDinh={checkChongChiDinh}
              />
            ),
            iconTab: <SVG.IcDonThuoc color={"var(--color-gray-primary)"} />,
            key: "9",
          },
        ]
      : []),
    {
      name: t("common.vatTuTheoXN"),
      component: (
        <ChiDinhVatTu
          isReadonly={isReadonly}
          isVatTuXN={true}
          layerId={state.activeKey === "10" ? layerId : null}
          checkChongChiDinh={checkChongChiDinh}
        />
      ),
      iconTab: <SVG.IcVatTu />,
      key: "10",
    },
    {
      name: t("quanLyNoiTru.phaCheThuocTheoDon"),
      component: (
        <PhaCheThuocTheoDon
          ref={refPhaCheThuocTheoDon}
          isReadonly={isReadonly}
        />
      ),
      iconTab: <SVG.IcDonThuoc color={"var(--color-gray-primary)"} />,
      roles: [ROLES["QUAN_LY_NOI_TRU"].PHA_CHE_THUOC_THEO_DON],
      key: "11",
    },
    {
      name: t("quanLyNoiTru.boChiDinh"),
      component: <BoChiDinh ref={refBoChiDinh} />,
      iconTab: <SVG.IcDichVu color={"var(--color-gray-primary)"} />,
      roles: [ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_TAB_BO_CHI_DINH],
      key: "12",
    },
  ];

  const renderRightAction = (tab) => {
    switch (tab) {
      case "11":
        return (
          <>
            <AuthWrapper
              accessRoles={[
                ROLES["QUAN_LY_NOI_TRU"].THEM_PHA_CHE_THUOC_THEO_DON,
              ]}
            >
              <Button
                type="success"
                minWidth={100}
                rightIcon={<SVG.IcAdd />}
                iconHeight={15}
                onClick={() => {
                  refPhaCheThuocTheoDon.current &&
                    refPhaCheThuocTheoDon.current.onThemMoi();
                }}
              >
                {t("common.themMoi")}
              </Button>
            </AuthWrapper>
          </>
        );
    }

    return null;
  };

  const filteredTabs = useMemo(() => {
    return listTabs.map((tab) => {
      if (["4", "8"].includes(tab.key) && checkLoaiCheDoAn) {
        return { ...tab, disabled: true };
      }
      return tab;
    });
  }, [listTabs, checkLoaiCheDoAn]);

  return (
    <Main top={16} bottom={0}>
      <Tabs.Left
        type="card"
        moreIcon={<CaretDownOutlined />}
        className="tab-main"
        activeKey={state.activeKey}
        onChange={onChangeTab}
      >
        {[...filteredTabs]
          .filter((x) => checkRole(x?.roles))
          .map((obj, i) => {
            return (
              <Tabs.TabPane
                key={obj.key}
                disabled={obj.disabled}
                tab={
                  <div>
                    {obj?.iconTab}
                    {obj?.name}
                  </div>
                }
              >
                <Tabs.TabBox
                  fixHeight={false}
                  title={obj?.name}
                  rightAction={renderRightAction(obj.key)}
                >
                  {obj?.component}
                </Tabs.TabBox>
              </Tabs.TabPane>
            );
          })}
      </Tabs.Left>
    </Main>
  );
};
export default ChiDinhDichVu;
