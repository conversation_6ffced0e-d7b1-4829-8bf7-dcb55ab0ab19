import React, { useEffect, useMemo, useRef, useState } from "react";
import { Main } from "./styled";
import { Collapse, message } from "antd";
import Header from "pages/khamBenh/ChiDinhDichVu/DichVuDaChiDinh/DanhSachChiDinh/Header";
import { useDispatch, useSelector } from "react-redux";
import {
  ENUM,
  GIOI_TINH_BY_VALUE,
  THIET_LAP_CHUNG,
  TRANG_THAI_MAU,
  LOAI_BIEU_MAU,
  LOAI_DICH_VU,
} from "constants/index";
import { useConfirm, useEnum, useLoading, useStore, useThietLap } from "hooks";
import { groupBy, uniq } from "lodash";
import { useTranslation } from "react-i18next";
import { CollapseWrapper } from "pages/khamBenh/ChiDinhDichVu/DichVuDaChiDinh/styled";
import TableMau from "./TableMau";
import { isArray } from "utils";
import ModalYeuCauTraDV from "../ModalYeuCauTraDV";
import printProvider from "data-access/print-provider";
import ModalHuyYeuCauTraDV from "../ModalHuyYeuCauTraDV";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { ModalSignPrint } from "components";

const { Panel } = Collapse;

const MauDaChiDinh = ({ ...props }) => {
  const { showConfirm } = useConfirm();
  const { isReadonly } = props;
  const { showLoading, hideLoading } = useLoading();

  const [state, _setState] = useState({
    activeKey: [],
    phieuLinhMau: null,
    listDvKemTheo: {},
    listDvVatTuKemTheo: {},
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    chiDinhMau: {
      inPhieu,
      onDeleteAll,
      getListChePhamMau,
      getListDichVuKemTheo,
      inPhieuNhapXuat,
      inPhieuYeuCauTraMau,
      inPhieuChiDinhXnKemMau,
      inPhieuXetNghiemHuyetHoc,
      getListDichVuVatTuKemTheo,
      inPhieuPhatDuTruMau,
    },
    thietLap: { getThietLap },
    baoCao: { onSearch, updateData },
    phieuIn: { getListPhieu, getPhieuInTheoMa },
    files: { getInfoBaoCao },
  } = useDispatch();
  const { dataPHIEU_LINH_MAU_KEM_DICH_VU_XET_NGHIEM = "" } = useSelector(
    (state) => state.thietLap
  );
  const refModalHoanDichVu = useRef(null);
  const refModalHuyHoanDichVu = useRef(null);
  const refModalSignPrint = useRef(null);
  const refPhieuLinhMau = useRef(null);

  const { t } = useTranslation();
  const [listLoaiDonThuoc] = useEnum(ENUM.LOAI_DON_THUOC);
  const listDvMau = useStore("chiDinhMau.listDvMau", []);
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const configData = useStore("chiDinhKhamBenh.configData", {});
  const [dataAN_PHIEU_LINH_MAU] = useThietLap(
    THIET_LAP_CHUNG.AN_PHIEU_LINH_MAU
  );
  const listBaoCao = useStore("baoCao.listAllBaoCao", []);
  const [dataKHONG_CHO_XOA_CHI_DINH_MAU_KHI_DA_DIEN_MA_TUI_MAU] = useThietLap(
    THIET_LAP_CHUNG.KHONG_CHO_XOA_CHI_DINH_MAU_KHI_DA_DIEN_MA_TUI_MAU
  );
  const [dataIN_PHIEU_TRUYEN_MAU_KET_NOI_LIS] = useThietLap(
    THIET_LAP_CHUNG.IN_PHIEU_TRUYEN_MAU_KET_NOI_LIS
  );
  const [dataIN_PHIEU_DU_TRU_MAU] = useThietLap(
    THIET_LAP_CHUNG.IN_PHIEU_DU_TRU_MAU,
    "false"
  );

  useEffect(() => {
    getThietLap({ ma: "PHIEU_LINH_MAU_KEM_DICH_VU_XET_NGHIEM" });
    onSearch({
      page: 0,
      size: 10,
      isTongHop: true,
      dataSearch: { ma: "EMR_BA268" },
    });
    return () => {
      updateData({ listAllBaoCao: [] });
    };
  }, []);

  useEffect(() => {
    if (configData && configData.nbDotDieuTriId) {
      getListChePhamMau({
        nbDotDieuTriId: configData.nbDotDieuTriId,
        chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
        dsTrangThaiHoan: [0, 10, 20, 40, 42],
      }).then((s) => {
        if (s?.data) {
          let grouped = groupBy(s?.data, "tenDon");
          setState({ activeKey: Object.keys(grouped || [])?.[0] });
        }
      });
      getListPhieu({
        nbDotDieuTriId: configData.nbDotDieuTriId,
        maManHinh: "007",
        maViTri: "00702",
        chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
        dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
      }).then((res) => {
        if (isArray(res, true)) {
          const phieuLinhMau = res.find((i) => i.ma === "P468");
          if (phieuLinhMau) {
            setState({ phieuLinhMau });
          }
        }
      });
    }
  }, [configData]);

  useEffect(() => {
    refPhieuLinhMau.current = () => {
      return state.phieuLinhMau;
    };
  }, [state.phieuLinhMau]);

  useEffect(async () => {
    if (listDvMau && listDvMau?.length > 0) {
      onGetListDichVuKemTheo(listDvMau);
    } else {
      setState({ listDvKemTheo: {}, listDvVatTuKemTheo: {} });
    }
  }, [listDvMau]);

  const onGetListDichVuKemTheo = async (listDvMau) => {
    const grouped = groupBy(listDvMau, "soPhieu");

    if (listDvMau && listDvMau?.length > 0) {
      const listDvKemTheoAll = {};
      const listDvVatTuKemTheoAll = {};

      await Promise.all(
        Object.entries(grouped).map(async ([key, items]) => {
          const { nbDotDieuTriId, phieuNhapXuatId } = items[0] || {};

          const [dvKemTheo, vatTuKemTheo] = await Promise.all([
            getListDichVuKemTheo({ nbDotDieuTriId, phieuNhapXuatId }),
            getListDichVuVatTuKemTheo({ nbDotDieuTriId, phieuNhapXuatId }),
          ]);

          listDvKemTheoAll[key] = dvKemTheo || [];
          listDvVatTuKemTheoAll[key] = vatTuKemTheo || [];
        })
      );
      //listDvKemTheo là dạng object các mảng dv kèm theo, key là số phiếu
      setState({
        listDvKemTheo: listDvKemTheoAll,
        listDvVatTuKemTheo: listDvVatTuKemTheoAll,
      });
    }
  };

  const dataNb = useMemo(() => {
    let gender = chiTietNguoiBenhNoiTru?.gioiTinh
      ? GIOI_TINH_BY_VALUE[chiTietNguoiBenhNoiTru?.gioiTinh]
      : "";

    let age =
      chiTietNguoiBenhNoiTru?.thangTuoi > 36 || chiTietNguoiBenhNoiTru?.tuoi
        ? `${chiTietNguoiBenhNoiTru?.tuoi} ${t("common.tuoi")}`
        : `${chiTietNguoiBenhNoiTru?.thangTuoi} ${t("common.thang")}`;
    return { gender, age };
  }, []);

  const refreshData = () => {
    getListChePhamMau({
      nbDotDieuTriId: configData.nbDotDieuTriId,
      chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
      dsTrangThaiHoan: [0, 10, 20, 40, 42],
    });
  };

  const onDelete =
    ({ listDvMau }) =>
    (e) => {
      if (
        dataKHONG_CHO_XOA_CHI_DINH_MAU_KHI_DA_DIEN_MA_TUI_MAU?.eval() &&
        listDvMau?.every((item) => item?.maTuiMau && item?.nguoiPhat1Id)
      ) {
        return message.error(
          t("quanLyNoiTru.mau.xoaKhongThanhCong_MauDaĐuocienThongMaTuiMau")
        );
      }

      showConfirm(
        {
          title: t("common.xoaDuLieu"),
          content: t("pttt.banCoChacChanMuonXoaPhieuMau"),
          cancelText: t("common.quayLai"),
          okText: t("common.dongY"),
          classNameOkText: "button-error",
          showBtnOk: true,
          typeModal: "error",
        },
        () => {
          try {
            showLoading();
            const payload = listDvMau.map((item) => {
              return item.id;
            });
            onDeleteAll(payload).then((s) => {
              let data = (s?.data || [])
                .filter((item) => {
                  return item.code !== 0 && item?.message;
                })
                ?.map((item) => item?.message);
              if (data?.length > 0) {
                message.error(
                  `${t("khamBenh.donThuoc.khongTheXoaDichVu")} :  ${uniq(
                    data
                  )?.join("; ")}`
                );
              } else {
                message.success(t("pttt.xoaThanhCongDichVu"));
              }

              getListChePhamMau({
                nbDotDieuTriId: configData.nbDotDieuTriId,
                chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
                chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
                dsTrangThaiHoan: [0, 10, 20, 40, 42],
              });
            });
          } catch (error) {
          } finally {
            hideLoading();
          }
        }
      );
    };

  const onTraMau = (listDvMau) => async (e) => {
    e.stopPropagation();
    e.preventDefault();
    const data = listDvMau || [];
    if (data?.length) {
      data.forEach((itemLoop) => {
        itemLoop.gioiTinh = dataNb?.gender;
        itemLoop.tuoi = dataNb?.age;
      });

      refModalHoanDichVu.current &&
        refModalHoanDichVu.current.show({ data }, () => {
          refreshData();
        });
    } else {
      message.error(t("khamBenh.chiDinh.khongCoDichVuThoaManDieuKienDeHoan"));
    }
  };

  const onHuyTraMau = (listDvMau) => async (e) => {
    e.stopPropagation();
    e.preventDefault();
    const data = listDvMau || [];
    if (data?.length) {
      data.forEach((itemLoop) => {
        itemLoop.gioiTinh = dataNb?.gender;
        itemLoop.tuoi = dataNb?.age;
      });

      refModalHuyHoanDichVu.current &&
        refModalHuyHoanDichVu.current.show({ data }, () => {
          refreshData();
        });
    } else {
      message.error(t("khamBenh.chiDinh.khongCoDichVuThoaManDieuKienDeHoan"));
    }
  };

  const onPrintPhieuLinhMau =
    (phieuNhapXuatId, loading, soPhieu, id) => async (e) => {
      // inPhieu(configData);
      e.stopPropagation();
      e.preventDefault();

      try {
        if (loading) showLoading();
        let phieuLinhMau = refPhieuLinhMau.current();
        if (phieuLinhMau) {
          if (checkIsPhieuKySo(phieuLinhMau)) {
            phieuLinhMau = {
              ...phieuLinhMau,
              dsSoPhieu: phieuLinhMau.dsSoPhieu.filter(
                (i) => i.soPhieu == phieuNhapXuatId
              ),
            };
            refModalSignPrint.current &&
              refModalSignPrint.current.showToSign(
                {
                  phieuKy: phieuLinhMau,
                  payload: {
                    nbDotDieuTriId: configData.nbDotDieuTriId,
                    chiDinhTuDichVuId: configData?.chiDinhTuDichVuId,
                    maManHinh: "007",
                    maViTri: "00702",
                    phieuNhapXuatId,
                    id,
                  },
                },
                async () => {
                  const res = await getListPhieu({
                    nbDotDieuTriId: configData.nbDotDieuTriId,
                    maManHinh: "007",
                    maViTri: "00702",
                    chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
                    dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                  });
                  let resfreshPhieu = (res || []).find(
                    (i) => i.ma === phieuLinhMau.ma
                  );
                  setState({ phieuLinhMau: resfreshPhieu });
                  return {
                    ...resfreshPhieu,
                    dsSoPhieu: (resfreshPhieu?.dsSoPhieu || []).filter(
                      (i) => i.soPhieu == phieuNhapXuatId
                    ),
                  };
                }
              );
          } else {
            await inPhieuNhapXuat({
              nbDotDieuTriId: configData.nbDotDieuTriId,
              phieuNhapXuatId,
            });
          }
        } else {
          await inPhieuNhapXuat({
            nbDotDieuTriId: configData.nbDotDieuTriId,
            phieuNhapXuatId,
          });
        }
      } catch (error) {
        console.error(error);
      } finally {
        if (loading) hideLoading();
      }
    };

  const onPrintPhieuTruyenMau = (phieuNhapXuatId, soPhieu) => async (e) => {
    e.stopPropagation();
    e.preventDefault();
    if (dataIN_PHIEU_TRUYEN_MAU_KET_NOI_LIS.eval()) {
      try {
        const res = await getInfoBaoCao("EMR_BA519");
        window.open(
          `/editor/bao-cao/EMR_BA519?nbDotDieuTriId=${configData.nbDotDieuTriId}&phieuXuatId=${phieuNhapXuatId}&chiDinhTuDichVuId=${phieuNhapXuatId}&boSung=${soPhieu}&baoCaoId=${res}&kySo=true`
        );
      } catch (error) {}
    } else {
      const res = await getInfoBaoCao("EMR_BA111");
      window.open(
        `/editor/bao-cao/EMR_BA111?nbDotDieuTriId=${configData.nbDotDieuTriId}&phieuXuatId=${phieuNhapXuatId}&baoCaoId=${res}`
      );
    }
  };

  const onPrintPhieuXetNghiemHuyetHoc = async (e) => {
    e.stopPropagation();
    e.preventDefault();

    const _maManHinh =
      configData.chiDinhTuLoaiDichVu == LOAI_DICH_VU.TO_DIEU_TRI
        ? "007"
        : "009";
    const _maViTri =
      configData.chiDinhTuLoaiDichVu == LOAI_DICH_VU.TO_DIEU_TRI
        ? "00701"
        : "00901";

    const res = await getPhieuInTheoMa({
      nbDotDieuTriId: configData.nbDotDieuTriId,
      chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
      dsChiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
      maManHinh: _maManHinh,
      maViTri: _maViTri,
      maPhieu: "P728",
    });

    if (checkIsPhieuKySo(res)) {
      refModalSignPrint.current &&
        refModalSignPrint.current.showToSign({
          phieuKy: res,
          payload: {
            maManHinh: _maManHinh,
            maViTri: _maViTri,
            nbDotDieuTriId: configData.nbDotDieuTriId,
            chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
            dsChiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
          },
        });
    } else {
      try {
        showLoading();
        const dsSoPhieuId = (res?.dsSoPhieu || []).map((x) => x.soPhieu);
        await inPhieuChiDinhXnKemMau({
          nbDotDieuTriId: configData.nbDotDieuTriId,
          phieuChiDinhId: res.baoCaoId,
          dsSoPhieuId: uniq(dsSoPhieuId),
        });
      } catch (err) {
        console.error(err);
      } finally {
        hideLoading();
      }
    }
  };

  const onPrintPhieuYCTraMau =
    (listPhieuMauYeuCauTra, phieuNhapXuatId) => async (e) => {
      e.stopPropagation();
      e.preventDefault();
      try {
        if (isArray(listBaoCao, true)) {
          let isEditor =
            listBaoCao[0].loai === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA;
          if (isEditor) {
            const res = await getInfoBaoCao("EMR_BA268");

            listPhieuMauYeuCauTra.forEach((i) => {
              window.open(`/editor/bao-cao/EMR_BA268/${i.id}?baoCaoId=${res}`);
            });
          } else {
            showLoading();
            const res = await inPhieuYeuCauTraMau({
              nbDotDieuTriId: configData.nbDotDieuTriId,
              dsId: listPhieuMauYeuCauTra.map((i) => i.id),
              phieuNhapXuatId,
            });
            if (isArray(res, true)) {
              const listUrl = (res || []).map((item) => {
                return item.file.pdf;
              });
              printProvider.printMergePdf(listUrl);
            }
          }
        } else {
          message.error(t("khoMau.inPhieuTraMauLoi"));
        }
      } finally {
        hideLoading();
      }
    };

  const onPrintPhieuPhatDuTruMau = (phieuNhapXuatId) => async (e) => {
    e.stopPropagation();
    e.preventDefault();

    try {
      showLoading();

      const res = await getPhieuInTheoMa({
        nbDotDieuTriId: configData.nbDotDieuTriId,
        chiDinhTuDichVuId: phieuNhapXuatId,
        dsChiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
        maManHinh: "007",
        maViTri: "00702",
        maPhieu: "P216",
      });

      if (checkIsPhieuKySo(res)) {
        refModalSignPrint.current?.showToSign({
          phieuKy: res,
          payload: {
            maManHinh: "007",
            maViTri: "00702",
            nbDotDieuTriId: configData.nbDotDieuTriId,
            chiDinhTuDichVuId: phieuNhapXuatId,
            dsChiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
            phieuNhapXuatId,
          },
        });
      } else {
        await inPhieuPhatDuTruMau({
          phieuNhapXuatId,
          nbDotDieuTriId: configData.nbDotDieuTriId,
        });
      }
    } catch (err) {
      console.error(err);
    } finally {
      hideLoading();
    }
  };

  const phieus = (phieuNhapXuatId, data, key, id) => {
    const listPhieuMauYeuCauTra = (data || []).filter(
      (i) => i.trangThai === TRANG_THAI_MAU.YEU_CAU_TRA
    );
    return [
      ...((dataAN_PHIEU_LINH_MAU || "").toLowerCase() == "true"
        ? []
        : [
            {
              key: 0,
              ten: t("phieuIn.inPhieuLinhMau"),
              api: onPrintPhieuLinhMau(phieuNhapXuatId, true, key, id),
            },
            {
              key: 1,
              ten: t("phieuIn.phieuChiDinhXnKemMau"),
              api: onPrintPhieuXetNghiemHuyetHoc,
            },
          ]),
      {
        key: 2,
        ten: t("phieuIn.inPhieuTruyenMau"),
        api: onPrintPhieuTruyenMau(phieuNhapXuatId, key),
      },
      {
        key: 3,
        ten: t("phieuIn.phieuDuTruVaCungCapMau"),
        api: async () => {
          try {
            const res = await getInfoBaoCao("EMR_BA235");
            window.open(
              `/editor/bao-cao/EMR_BA235/${phieuNhapXuatId}?baoCaoId=${res}`
            );
          } catch (error) {
            console.error(error);
          }
        },
      },
      ...(isArray(listPhieuMauYeuCauTra, true)
        ? [
            {
              key: 4,
              ten: t("phieuIn.inPhieuYeuCauTraMau"),
              api: onPrintPhieuYCTraMau(listPhieuMauYeuCauTra, phieuNhapXuatId),
            },
          ]
        : []),
      // {
      //   key: 5,
      //   ten: t("phieuIn.phieuChiDinhXnKemMau"),
      //   api: onPrintPhieuChiDinhXnKemMau(soPhieuId, id),
      // },
      ...(dataIN_PHIEU_DU_TRU_MAU?.eval()
        ? [
            {
              key: 5,
              ten: t("phieuIn.phieuPhatDuTruMau"),
              api: onPrintPhieuPhatDuTruMau(phieuNhapXuatId),
            },
          ]
        : []),
    ];
  };

  const listPanel = useMemo(() => {
    let grouped = groupBy(listDvMau, "soPhieu");

    return Object.keys(grouped || []).map((key) => {
      let groupByIdArr = grouped[key];
      return {
        header: (
          <Header
            title={`${t("quanLyNoiTru.dvNoiTru.mau")}: ${t(
              "common.soPhieu"
            )} ${key}`}
            isReadonly={isReadonly}
            isCollapsed={state.activeKey.includes(key)}
            isDisplayPrintPhieu={true}
            isDisplayTraMau={isArray(groupByIdArr, true)}
            toolTipInPhieu={t("common.inPhieu")}
            listPhieu={phieus(
              grouped[key]?.[0]?.phieuNhapXuatId,
              groupByIdArr,
              key,
              grouped[key]?.[0]?.id
            )}
            onInPhieu={
              dataPHIEU_LINH_MAU_KEM_DICH_VU_XET_NGHIEM?.toLowerCase() == "true"
                ? onPrintPhieuLinhMau(
                    grouped[key]?.[0]?.phieuNhapXuatId,
                    false,
                    key,
                    grouped[key]?.[0]?.id
                  )
                : false
            }
            onDelete={onDelete({
              listDvMau: groupByIdArr,
              tenDon: grouped[key]?.[0].tenDon,
            })}
            onTraMau={onTraMau(groupByIdArr)}
            onHuyTraMau={onHuyTraMau(groupByIdArr)}
          />
        ),
        content: (
          <TableMau
            title={t("quanLyNoiTru.dvNoiTru.mau")}
            listDvMau={groupByIdArr}
            nbDotDieuTriId={configData.nbDotDieuTriId}
            isReadonly={isReadonly}
            listDvKemTheo={state.listDvKemTheo?.[key]}
            listDvVatTuKemTheo={state.listDvVatTuKemTheo?.[key]}
            onGetListDichVuKemTheo={onGetListDichVuKemTheo}
          />
        ),
        key,
      };
    });
  }, [
    listDvMau,
    configData,
    listLoaiDonThuoc,
    state.activeKey,
    state.phieuLinhMau,
    isReadonly,
    dataPHIEU_LINH_MAU_KEM_DICH_VU_XET_NGHIEM,
    state.listDvKemTheo,
    state.listDvVatTuKemTheo,
  ]);

  const onCollapsed = (value) => {
    setState({
      activeKey: value,
    });
  };

  return (
    <Main>
      <div className="collapse-content">
        <CollapseWrapper
          bordered={false}
          activeKey={state.activeKey}
          // accordion
          onChange={onCollapsed}
        >
          {listPanel.map((panel) => (
            <Panel key={panel.key} header={panel.header}>
              {panel.content}
            </Panel>
          ))}
        </CollapseWrapper>
      </div>
      <ModalYeuCauTraDV ref={refModalHoanDichVu} />
      <ModalHuyYeuCauTraDV ref={refModalHuyHoanDichVu} />
      <ModalSignPrint ref={refModalSignPrint} />
    </Main>
  );
};

export default MauDaChiDinh;
