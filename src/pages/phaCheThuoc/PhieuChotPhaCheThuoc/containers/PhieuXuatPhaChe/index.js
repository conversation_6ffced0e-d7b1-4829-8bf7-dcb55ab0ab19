import React, {
  useEffect,
  useMemo,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import { Checkbox, TableWrapper, Tooltip } from "components";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { Wrapper } from "./styled";
import { SVG } from "assets";
import { useEnum, useStore, useConfirm, useLoading } from "hooks";
import { ENUM, TRANG_THAI_PHA_CHE_THUOC } from "constants/index";
import { useParams } from "react-router-dom";
import { cloneDeep, isNumber } from "lodash";
import { roundToDigits } from "utils/index";
import { xuatFileExcelKho } from "utils/kho-utils";
import { Input } from "antd";

const { Column, Setting } = TableWrapper;

const DsPhieuXuatPhaChe = ({}, ref) => {
  const { t } = useTranslation();
  const { id } = useParams();
  const { showConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();
  const refSettings = useRef(null);

  const refInit = useRef({
    dataSourceDsPhieuPhaChe: [],
  });
  const [state, _setState] = useState(refInit.current);
  const setState = (data = {}) => _setState((pre) => ({ ...pre, ...data }));

  const { dataSortColumn } = useSelector((state) => state.nbPhaChe);

  const [listTrangThaiPhaCheThuoc] = useEnum(ENUM.TRANG_THAI_PHA_CHE_THUOC);

  const chiTietPhieuPhaCheTheoChot = useStore(
    "nbChotPhaCheThuoc.chiTietPhieuPhaCheTheoChot",
    {}
  );

  const thongTinPhieuPhaChe = useStore(
    "nbChotPhaCheThuoc.thongTinPhieuPhaChe",
    []
  );

  const {
    nbChotPhaCheThuoc: {
      getChiTietPhieuPhaCheTheoChot,
      xoaThuocPhieuLinh,
      getPhieuBanGiaoDichTruyen,
    },
  } = useDispatch();

  const getChiTietPhieu = async () => {
    getChiTietPhieuPhaCheTheoChot(id);
  };

  useImperativeHandle(ref, () => ({
    getDsPhaCheThuocChiTietId: () => {
      return dataSource.map((item) => ({
        id: item.id,
      }));
    },
    refreshPhieu: getChiTietPhieu,
  }));

  useEffect(() => {
    if (id) {
      getChiTietPhieu();
    }
  }, [id]);

  const dataSource = useMemo(() => {
    let _data = chiTietPhieuPhaCheTheoChot?.dsPhaCheThuocChiTiet || [],
      result = [];

    result = cloneDeep(_data).filter((item) => !item.dungKemId);
    result = result.map((item, index) => {
      let _dsDungKem = _data.filter((x) => x.dungKemId == item.nbDvKhoId);
      if (_dsDungKem.length > 0) {
        item.children = _dsDungKem.map((i) => ({
          ...i,
          indexParent: index,
        }));
        item.dungMoi = _dsDungKem.map((x) => x.tenDichVu).join(", ");
      }

      return item;
    });

    if (state?.soPhieu) {
      const keyword = String(state.soPhieu).toLowerCase();
      result = result.filter((item) =>
        String(item?.soPhieu ?? "").toLowerCase().includes(keyword)
      );
    }

    setState({ expandedRowKeys: result.map((item) => item.id) });

    return [...result];
  }, [chiTietPhieuPhaCheTheoChot?.dsPhaCheThuocChiTiet, state.soPhieu]);

  const onClickSort = (key, value) => { };

  const onSearchInput = (key) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else value = e;
    setState({ [key]: value });
  };

  const onExportExcel = async () => {
    try {
      showLoading();
      const s = await getPhieuBanGiaoDichTruyen(id);
      xuatFileExcelKho(s);
    } catch (error) {
      console.log("error", error);
    } finally {
      hideLoading();
    }
  };

  const onDelete = (record) => (e) => {
    e.stopPropagation();
    showConfirm(
      {
        title: t("nhaThuoc.xoaThuoc"),
        content: `${t("common.banChacChanMuonXoa")} ${record?.tenNb || ""}?`,
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      async () => {
        try {
          showLoading();

          await xoaThuocPhieuLinh({
            id: id,
            nbDotDieuTriId: record.nbDotDieuTriId,
          });

          getChiTietPhieu();
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    );
  };

  const columnsDsPhieuPhaChe = [
    Column({
      title: t("common.stt"),
      width: 60,
      dataIndex: "stt",
      key: "stt",
      align: "center",
      render: (item, data, index) => {
        if (isNumber(data.indexParent)) return "";
        return index + 1;
      },
    }),
    Column({
      title: t("danhMuc.maThuoc"),
      width: 120,
      dataIndex: "maDichVu",
      key: "maDichVu",
      i18Name: "danhMuc.maThuoc",
    }),
    Column({
      title: t("common.tenThuoc"),
      width: 200,
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      i18Name: "common.tenThuoc",
      render: (item, data, index) => {
        return `${item} ${data.hamLuong || ""}`;
      },
    }),
    Column({
      title: t("danhMuc.hoTen"),
      width: 160,
      dataIndex: "tenNb",
      key: "tenNb",
      i18Name: "common.tenNb",
      sort_key: "tenNb",
      dataSort: dataSortColumn["tenNb"] || "",
      onClickSort,
    }),
    Column({
      title: t("common.bacSiChiDinh"),
      width: 160,
      dataIndex: "tenBacSiChiDinh",
      key: "tenBacSiChiDinh",
      i18Name: "common.bacSiChiDinh",
      sort_key: "tenBacSiChiDinh",
      dataSort: dataSortColumn["tenBacSiChiDinh"] || "",
      onClickSort,
    }),
    Column({
      title: t("common.cachDung"),
      width: 200,
      dataIndex: "cachDung",
      key: "cachDung",
      i18Name: "common.cachDung",
      sort_key: "cachDung",
      dataSort: dataSortColumn["cachDung"] || "",
      onClickSort,
    }),
    Column({
      title: t("common.thoiGianBatDau"),
      width: 160,
      dataIndex: "thoiGianBatDau",
      key: "thoiGianBatDau",
      i18Name: "common.thoiGianBatDau",
      sort_key: "thoiGianBatDau",
      dataSort: dataSortColumn["thoiGianBatDau"] || "",
      onClickSort,
      render: (item) => item?.toDateObject()?.format("dd/MM/yyyy HH:mm:ss"),
    }),
    Column({
      title: t("common.thoiGianPhaChe"),
      width: 160,
      dataIndex: "thoiGianPhaChe",
      key: "thoiGianPhaChe",
      i18Name: "common.thoiGianPhaChe",
      sort_key: "thoiGianPhaChe",
      dataSort: dataSortColumn["thoiGianPhaChe"] || "",
      onClickSort,
      render: (item) => item?.toDateObject()?.format("dd/MM/yyyy HH:mm:ss"),
    }),
    Column({
      title: t("common.ngayThucHien"),
      width: 160,
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      i18Name: "common.ngayThucHien",
      sort_key: "thoiGianThucHien",
      dataSort: dataSortColumn["thoiGianThucHien"] || "",
      onClickSort,
      render: (item) => item?.toDateObject()?.format("dd/MM/yyyy HH:mm:ss"),
    }),
    Column({
      title: t("common.soPhieu"),
      width: 100,
      dataIndex: "soPhieu",
      key: "soPhieu",
      i18Name: "common.soPhieu",
      align: "right",
      renderSearch: (
        <Input
          placeholder={t("common.timKiem")}
          value={state.soPhieu}
          onChange={onSearchInput("soPhieu")}
        />
      ),
    }),
    Column({
      title: t("phacDoDieuTri.truocPhaChe"),
      width: 120,
      dataIndex: "truocPhaChe",
      key: "truocPhaChe",
      i18Name: "phacDoDieuTri.truocPhaChe",
      align: "center",
      render: (item, record) => (
        <Checkbox
          checked={item}
          disabled
        />
      ),
    }),
    Column({
      title: t("phacDoDieuTri.sauPhaChe"),
      width: 120,
      dataIndex: "sauPhaChe",
      key: "sauPhaChe",
      i18Name: "phacDoDieuTri.sauPhaChe",
      align: "center",
      render: (item, record) => (
        <Checkbox
          checked={item}
          disabled
        />
      ),
    }),
    Column({
      title: <>{t("phaCheThuoc.soLuongDungThuCap")}</>,
      width: 100,
      key: "soLuongDungThuCap",
      i18Name: "phaCheThuoc.soLuongDungThuCap",
      align: "right",
      render: (item, data, index) => {
        let soLuongDungThuCap = data.soLuongYeuCau - data.soLuongHuy;
        return soLuongDungThuCap > 0 ? roundToDigits(soLuongDungThuCap, 3) : "";
      },
    }),
    Column({
      title: <>{t("common.soLuongSoCap")}</>,
      width: 100,
      dataIndex: "soLuongYeuCauSoCap",
      key: "soLuongYeuCauSoCap",
      i18Name: "common.soLuongSoCap",
      align: "right",
    }),
    Column({
      title: t("phaCheThuoc.soLuongHuy"),
      width: 100,
      dataIndex: "soLuongHuy",
      key: "soLuongHuy",
      i18Name: "phaCheThuoc.soLuongHuy",
      align: "right",
    }),
    Column({
      title: <>{t("phaCheThuoc.soLuongQuyDoi")}</>,
      width: 100,
      key: "soLuongQuyDoi",
      i18Name: "phaCheThuoc.soLuongQuyDoi",
      align: "right",
      render: (item, data, index) => {
        const soLuongQuyDoi =
          data.dungTich && data.heSoDinhMuc
            ? ((data.soLuong - data.soLuongHuy) * data.dungTich) /
              data.heSoDinhMuc
            : null;
        return soLuongQuyDoi > 0 ? roundToDigits(soLuongQuyDoi, 3) : "";
      },
    }),
    Column({
      title: <>{t("phaCheThuoc.donViTinhThuCap")}</>,
      width: 100,
      dataIndex: "tenDonViTinh",
      key: "tenDonViTinh",
      i18Name: "phaCheThuoc.donViTinhThuCap",
      align: "right",
    }),
    Column({
      title: <>{t("phaCheThuoc.donViTinhSoCap")}</>,
      width: 100,
      dataIndex: "tenDvtSoCap",
      key: "tenDvtSoCap",
      i18Name: "phaCheThuoc.donViTinhSoCap",
      align: "right",
    }),
    Column({
      title: (
        <div className="col-tien-ich">
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </div>
      ),
      width: "100px",
      dataIndex: "action",
      key: "action",
      fixed: "right",
      ignore: true,
      align: "center",
      render: (_, record) => {
        return (
          <>
            {chiTietPhieuPhaCheTheoChot.trangThai ==
              TRANG_THAI_PHA_CHE_THUOC.TAO_MOI && (
              <Tooltip title={t("nhaThuoc.xoaThuoc")} placement="bottomLeft">
                <SVG.IcDelete onClick={onDelete(record)} className="icon" />
              </Tooltip>
            )}

            <Tooltip title={t("phaCheThuoc.inNhanPc")} placement="bottomLeft">
              <SVG.IcPrint className="icon" />
            </Tooltip>
          </>
        );
      },
    }),
  ];

  return (
    <Wrapper noPadding={true} bottom={0}>
      <div className="phieuXuatPhaChe-header">
        <div className="phieuXuatPhaChe-header-left">
          <div className="title">{t("phaCheThuoc.phieuChotPhaCheThuoc")}</div>
        </div>
        <div className="phieuXuatPhaChe-header-right">
          <div className="phieuXuatPhaChe-header-right-top">
            <div className="phieuXuatPhaChe-header-right-ticket bottom">
              <div className="phieuXuatPhaChe-header-right-ticket bottom item">
                <div className="label">{t("phaCheThuoc.soPhieu")}: </div>
                <b>{chiTietPhieuPhaCheTheoChot.soPhieu}</b>
              </div>
              <div className="phieuXuatPhaChe-header-right-ticket bottom item">
                <div className="label">
                  {t("phaCheThuoc.soPhieuNhapXuat")}:{" "}
                </div>
                <b>{thongTinPhieuPhaChe?.soPhieuNhapXuat}</b>
              </div>
              <div className="phieuXuatPhaChe-header-right-ticket bottom item">
                <div className="label">{t("phaCheThuoc.ngayLap")}: </div>
                <b>
                  {moment(chiTietPhieuPhaCheTheoChot.ngayLap).format(
                    "DD/MM/YYYY HH:mm:ss"
                  )}
                </b>
              </div>
              <div className="phieuXuatPhaChe-header-right-ticket bottom item">
                <div className="label">{t("phaCheThuoc.nguoiLap")}: </div>
                <b>{chiTietPhieuPhaCheTheoChot.nguoiTaoPhieu?.ten || ""}</b>
              </div>
              <div className="phieuXuatPhaChe-header-right-ticket bottom item">
                <div className="label">{t("common.trangThai")}: </div>
                <b>
                  {listTrangThaiPhaCheThuoc.find(
                    (x) => x.id == chiTietPhieuPhaCheTheoChot.trangThai
                  )?.ten || ""}
                </b>
              </div>
              <div
                style={{
                  flex: 1,
                  textAlign: "right",
                  paddingRight: 16,
                  cursor: "pointer",
                }}
              >
                <Tooltip
                  title={t("phaCheThuoc.xuatFileExcel")}
                  placement="topLeft"
                >
                  <SVG.IcDownload
                    onClick={onExportExcel}
                    style={{ width: 24, height: 24 }}
                  />
                </Tooltip>
              </div>
            </div>
          </div>
          <div className="phieuXuatPhaChe-header-right-bottom"></div>
        </div>
      </div>
      <div className="dsPhieuPhaChe">
        <div className="dsPhieuPhaChe-header">
          <div className="dsPhieuPhaChe-header-left">
            {t("phaCheThuoc.danhSachPhieuPhaChe")}
          </div>
        </div>
        <div className="dsPhieuPhaChe-table">
          <TableWrapper
            rowKey={(record) => record?.id}
            styleWrap={{
              height: "100%",
              tableCell: { padding: "3px 4px" },
            }}
            scroll={{ y: false, x: 400 }}
            columns={columnsDsPhieuPhaChe}
            expandedRowKeys={state.expandedRowKeys}
            onExpand={(expanded, record) => {
              if (expanded) {
                setState({
                  expandedRowKeys: [...state.expandedRowKeys, record?.id],
                });
              } else {
                setState({
                  expandedRowKeys: state.expandedRowKeys.filter(
                    (key) => key !== record?.id
                  ),
                });
              }
            }}
            tableName="DS_PHIEU_XUAT_PHA_CHE"
            ref={refSettings}
            dataSource={dataSource}
          />
        </div>
      </div>
      <div className="phieuXuatKho"></div>
    </Wrapper>
  );
};

export default forwardRef(DsPhieuXuatPhaChe);
