import React, { useMemo } from "react";
import { Button, Dropdown, Page, Tabs } from "components";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";
import { useRefFunc, useSearchParams } from "hooks";
import { SVG } from "assets";
import TiepDon from "./TiepDon";
import PhauThuat from "./PhauThuat";
import KhamBenh from "./KhamBenh";
import ChiTietPhieuThu from "./ChiTietPhieuThu";
import ActionDashboard from "./Dashboard/TongQuanBenhVien/Action";
import ActionChiTietPhieuThu from "./ChiTietPhieuThu/Action";
import ActionChiTietDonThuoc from "./NhaThuoc/Action";
import PopupChiDinhThuocKhamBenh from "./KhamBenh/PopupChiDinhThuocKhamBenh";
import PopupChiDinhDvktKhamBenh from "./KhamBenh/PopupChiDinhDvktKhamBenh";
import PopupChiDinhDvktNoiTru from "./NoiTru/PopupChiDinhDvkt/SapXepTruong";
import HienThiPopupChiDinhDvktNoiTru from "./NoiTru/PopupChiDinhDvkt/ThietLapTenHienThi";
import PopupChiDinhThuocNoiTru from "./PopupChiDinhThuocNoiTru";
import PopupChiDinhThuocCdha from "./PopupChiDinhThuocCdha";
import PopupChiDinhThuocPttt from "./PopupChiDinhThuocPttt";
import TongQuanBenhVien from "./Dashboard/TongQuanBenhVien";
import ThongTinNbTiepDon from "./ThongTinNbTiepDon";
import ThietLapHienThiTenPtttCdha from "./ThietLapHienThiTenPtttCdha";
import ThietLapDanhMuc from "./ThietLapDanhMuc";
import { checkRole } from "lib-utils/role-utils";
import { ROLES } from "constants/index";
import ChiTietDonThuoc from "./NhaThuoc/ChiTietDonThuoc";
import SangLocDinhDuong from "./SangLocDinhDuong";
import ThietLapGiaoDien from "./ThietLapGiaoDien";
import NoiTru from "./NoiTru";
import { Main } from "./styled";

const TuyChinhGiaoDienPhanMem = () => {
  const { t } = useTranslation();
  const history = useHistory();

  const { searchParams, setSearchParams } = useSearchParams();

  const tab = searchParams.tab || "tiepDon";

  const listTabs = [
    {
      key: "tiepDon",
      name: t("tiepDon.tiepDon"),
      component: <TiepDon />,
      iconTab: <SVG.IcTuyChinhTiepDon />,
      isShow: true,
    },
    {
      key: "thongTinNguoiBenhTiepDon",
      name: t("tuyChinhGiaoDien.thongTinNguoiBenhTiepDon"),
      component: <ThongTinNbTiepDon />,
      iconTab: <SVG.IcTuyChinhTiepDon />,
      isShow: true,
    },
    {
      key: "chiTietPhieuThu",
      name: t("thuNgan.chiTietPhieuThu"),
      component: <ChiTietPhieuThu />,
      iconTab: <SVG.IcThuTamUng />,
      isShow: true,
    },
    {
      key: "pttt",
      name: t("pttt.phauThuatThuThuat"),
      component: <PhauThuat />,
      iconTab: <SVG.IcThongTinPttt />,
      isShow: true,
    },
    {
      key: "thietLapViTriChamCongPtttCdha",
      name: t("tuyChinhGiaoDien.thietLapViTriChamCongPtttCdha"),
      component: <ThietLapHienThiTenPtttCdha />,
      iconTab: <SVG.IcThongTinPttt />,
      isShow: true,
    },
    {
      key: "popupChiDinhThuocKhamBenh",
      name: t("khamBenh.popupChiDinhThuoc"),
      component: <PopupChiDinhThuocKhamBenh />,
      iconTab: <SVG.IcDonThuoc color={"var(--color-gray-primary)"} />,
      isShow: true,
    },
    {
      key: "popupChiDinhDvktKhamBenh",
      name: t("khamBenh.popupChiDinhDvkt"),
      component: <PopupChiDinhDvktKhamBenh />,
      iconTab: <SVG.IcDichVu />,
      isShow: true,
    },
    {
      key: "popupChiDinhDvktNoiTru",
      name: t("tuyChinhGiaoDien.popupChiDinhDvktToDieuTriNoiTru"),
      component: <PopupChiDinhDvktNoiTru />,
      iconTab: <SVG.IcDichVu />,
      isShow: true,
    },
    {
      key: "thietLapHienThiPopupChiDinhDvktToDieuTriNoiTru",
      name: t(
        "tuyChinhGiaoDien.thietLapHienThiPopupChiDinhDvktToDieuTriNoiTru"
      ),
      component: <HienThiPopupChiDinhDvktNoiTru />,
      iconTab: <SVG.IcDichVu />,
      isShow: true,
    },
    {
      key: "popupChiDinhThuocNoiTru",
      name: t("quanLyNoiTru.popupChiDinhThuoc"),
      component: <PopupChiDinhThuocNoiTru />,
      iconTab: <SVG.IcDonThuoc color={"var(--color-gray-primary)"} />,
      isShow: true,
    },
    {
      key: "popupChiDinhThuocCdha",
      name: t("cdha.popupChiDinhThuoc"),
      component: <PopupChiDinhThuocCdha />,
      iconTab: <SVG.IcDonThuoc color={"var(--color-gray-primary)"} />,
      isShow: true,
    },
    {
      key: "popupChiDinhThuocPttt",
      name: t("pttt.popupChiDinhThuoc"),
      component: <PopupChiDinhThuocPttt />,
      iconTab: <SVG.IcDonThuoc color={"var(--color-gray-primary)"} />,
      isShow: true,
    },
    {
      key: "khamBenh",
      name: t("khamBenh.khamBenh"),
      component: <KhamBenh />,
      iconTab: <SVG.IcKhamBenh />,
      isShow: true,
    },
    {
      key: "dashboard",
      name: t("dashboard.dashboardTongQuanBenhVien"),
      component: <TongQuanBenhVien />,
      iconTab: <SVG.IcChart />,
      isShow: true,
    },
    {
      key: "chiTietNhaThuoc",
      name: t("dashboard.chiTietNhaThuoc"),
      component: <ChiTietDonThuoc />,
      iconTab: <SVG.IcNhaThuoc />,
      isShow: true,
    },
    {
      key: "thietLapDanhMuc",
      name: t("tuyChinhGiaoDien.thietLapDanhMuc"),
      component: <ThietLapDanhMuc />,
      iconTab: <SVG.IcEdit />,
      isShow: true,
    },
    {
      key: "sangLocDinhDuong",
      name: t("quanLyNoiTru.sangLocDinhDuong"),
      component: <SangLocDinhDuong />,
      iconTab: <SVG.IcEdit />,
      isShow: true,
    },
    {
      key: "thietLapGiaoDien",
      name: t("tuyChinhGiaoDien.thietLapGiaoDien"),
      component: <ThietLapGiaoDien />,
      iconTab: <SVG.IcEdit />,
      isShow: true,
    },
    {
      key: "noiTru",
      name: t("tuyChinhGiaoDien.noiTru"),
      component: <NoiTru />,
      iconTab: <SVG.IcEdit />,
      isShow: true,
    },
  ];

  const onChange = (tab) => {
    setSearchParams({ tab }, { replace: true });
  };
  const renderTitleTab = (tab) => {
    return tab.name;
  };
  const onSapXepVungThongTin = () => {
    switch (tab) {
      case "tiepDon":
        history.push(
          "/quan-tri/tuy-chinh-giao-dien-phan-mem/tiep-don/sap-xep-vung-thong-tin-tiep-don"
        );
        break;
      case "khamBenh":
        history.push(
          "/quan-tri/tuy-chinh-giao-dien-phan-mem/kham-benh/sap-xep-vung-thong-tin-kham-benh"
        );
        break;

      default:
        break;
    }
  };

  const menuTiepDon = useMemo(() => {
    const menu = [
      { key: 0, ten: t("tuyChinhGiaoDien.thongTinChung") },
      { key: 1, ten: t("tuyChinhGiaoDien.thongTinCaNhan") },
      { key: 2, ten: t("tuyChinhGiaoDien.thongTinBHYT") },
      { key: 3, ten: t("tuyChinhGiaoDien.thongTinBoSung") },
      { key: 4, ten: t("tuyChinhGiaoDien.thongTinSinhHieu") },
    ];

    const onClick = (item, index) => () => {
      switch (index) {
        case 0:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/tiep-don/sap-xep-truong-thong-tin?type=0"
          );
          break;
        case 1:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/tiep-don/sap-xep-truong-thong-tin?type=1"
          );
          break;
        case 2:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/tiep-don/sap-xep-truong-thong-tin?type=2"
          );
          break;
        case 3:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/tiep-don/sap-xep-truong-thong-tin?type=3"
          );
          break;
        case 4:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/tiep-don/sap-xep-truong-thong-tin?type=4"
          );
          break;
      }
    };
    return menu.map((item, index) => ({
      key: index,
      label: <a onClick={onClick(item, index)}>{item.ten}</a>,
    }));
  }, []);

  const menuKhamBenh = useMemo(() => {
    const menu = [
      { key: 0, ten: t("tuyChinhGiaoDien.khamBenh.hanhChinh") },
      { key: 1, ten: t("tuyChinhGiaoDien.khamBenh.chanDoan") },
      { key: 2, ten: t("tuyChinhGiaoDien.khamBenh.sinhHieu") },
      { key: 3, ten: t("tuyChinhGiaoDien.khamBenh.hoiBenh") },
      { key: 4, ten: t("tuyChinhGiaoDien.khamBenh.khamXet") },
      { key: 5, ten: t("tuyChinhGiaoDien.khamBenh.thongTinKham") },
      { key: 6, ten: t("khamBenh.khamSan.thongTinKhamSan") },
      { key: 7, ten: t("khamBenh.khamOm.thongTinMat") },
      { key: 8, ten: t("khamBenh.thongTinTienSu") },
    ];

    const onClick = (item, index) => () => {
      switch (index) {
        case 0:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/kham-benh/sap-xep-truong-thong-tin?type=0"
          );
          break;
        case 1:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/kham-benh/sap-xep-truong-thong-tin?type=1"
          );
          break;
        case 2:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/kham-benh/sap-xep-truong-thong-tin?type=2"
          );
          break;
        case 3:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/kham-benh/sap-xep-truong-thong-tin?type=3"
          );
          break;
        case 4:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/kham-benh/sap-xep-truong-thong-tin?type=4"
          );
          break;
        case 5:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/kham-benh/sap-xep-truong-thong-tin?type=5"
          );
          break;
        case 6:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/kham-benh/sap-xep-truong-thong-tin?type=6"
          );
          break;
        case 7:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/kham-benh/sap-xep-truong-thong-tin?type=7"
          );
        case 8:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/kham-benh/sap-xep-truong-thong-tin?type=8"
          );
          break;
      }
    };
    return menu.map((item, index) => ({
      key: index,
      label: <a onClick={onClick(item, index)}>{item.ten}</a>,
    }));
  }, []);

  const menuNhaThuoc = useMemo(() => {
    const menu = [
      { key: 0, ten: t("tuyChinhGiaoDien.nhaThuoc.chiTietDonThuoc") },
    ];

    const onClick = (item, index) => () => {
      switch (index) {
        case 0:
          history.push(
            "/quan-tri/tuy-chinh-giao-dien-phan-mem/chi-tiet-don-thuoc/sap-xep-truong-thong-tin?type=0"
          );
          break;
      }
    };
    return menu.map((item, index) => ({
      key: index,
      label: <a onClick={onClick(item, index)}>{item.ten}</a>,
    }));
  }, []);

  const onClickSapXepTruong = useRefFunc(() => {
    switch (tab) {
      case "pttt":
        history.push(
          "/quan-tri/tuy-chinh-giao-dien-phan-mem/phau-thuat/sap-xep-truong-thong-tin?type=0"
        );
        break;
      case "popupChiDinhThuocKhamBenh":
        history.push(
          "/quan-tri/tuy-chinh-giao-dien-phan-mem/kham-benh/chi-dinh-thuoc/sap-xep-truong-thong-tin?type=0"
        );
        break;
      case "popupChiDinhDvktKhamBenh":
        history.push(
          "/quan-tri/tuy-chinh-giao-dien-phan-mem/kham-benh/chi-dinh-dvkt/sap-xep-truong-thong-tin?type=0"
        );
        break;
      case "popupChiDinhDvktNoiTru":
        history.push(
          "/quan-tri/tuy-chinh-giao-dien-phan-mem/noi-tru/chi-dinh-dvkt/sap-xep-truong-thong-tin?type=0"
        );
        break;
      case "thietLapHienThiPopupChiDinhDvktToDieuTriNoiTru":
        history.push(
          "/quan-tri/tuy-chinh-giao-dien-phan-mem/noi-tru/chi-dinh-dvkt/thiet-lap-ten-hien-thi-truong-thong-tin?type=0"
        );
        break;
      case "popupChiDinhThuocNoiTru":
        history.push(
          "/quan-tri/tuy-chinh-giao-dien-phan-mem/noi-tru/sap-xep-truong-thong-tin?type=0"
        );
        break;
      case "popupChiDinhThuocCdha":
        history.push(
          "/quan-tri/tuy-chinh-giao-dien-phan-mem/cdha/sap-xep-truong-thong-tin?type=0"
        );
        break;
      case "popupChiDinhThuocPttt":
        history.push(
          "/quan-tri/tuy-chinh-giao-dien-phan-mem/pttt/sap-xep-truong-thong-tin?type=0"
        );
        break;
      case "thietLapViTriChamCongPtttCdha":
        history.push(
          "/quan-tri/tuy-chinh-giao-dien-phan-mem/thiet-lap-vi-tri-cham-cong-pttt-cdha"
        );
        break;
      case "thongTinNguoiBenhTiepDon":
        history.push(
          "/quan-tri/tuy-chinh-giao-dien-phan-mem/thong-tin-nguoi-benh-tiep-don/sap-xep-truong-thong-tin?type=THONG_TIN_NB_TIEP_DON"
        );
        break;
      case "thietLapDanhMuc":
        history.push(
          "/quan-tri/tuy-chinh-giao-dien-phan-mem/thiet-lap-danh-muc"
        );
        break;
      case "sangLocDinhDuong":
        let path =
          searchParams.screen === "1" || !searchParams.screen
            ? "phieu-slnb-nhap-vien"
            : searchParams.screen === "2"
            ? "phieu-slphu-nu-co-thai"
            : searchParams.screen === "3"
            ? "phieu-slnb-noi-tru"
            : searchParams.screen === "4"
            ? "phieu-slnb-nhap-vien-khong-mang-thai"
            : searchParams.screen === "5"
            ? "phieu-slbenh-nhi-noi-tru"
            : searchParams.screen === "6"
            ? "phieu-slbenh-nhi-ngoai-tru"
            : searchParams.screen === "7"
            ? "phieu-sl-tre-em"
            : "phieu-slnb-nhap-vien-khong-mang-thai";

        history.push(
          `/quan-tri/tuy-chinh-giao-dien-phan-mem/sang-loc-dinh-duong/${path}`
        );
        break;

      default:
        break;
    }
  });

  const renderActionRight = useMemo(() => {
    const getDropdownMenu = () => {
      const menuMap = {
        tiepDon: menuTiepDon,
        khamBenh: menuKhamBenh,
        chiTietNhaThuoc: menuNhaThuoc,
      };
      return { items: menuMap[tab] || {} };
    };

    const renderTiepDonKhamBenh = () => (
      <>
        <Button type="primary" onClick={onSapXepVungThongTin}>
          {t("tuyChinhGiaoDien.sapXepVungThongTin")}
        </Button>
        <Dropdown menu={getDropdownMenu()} placement="topCenter">
          <Button
            rightIcon={<SVG.IcMore />}
            iconHeight={15}
            minWidth={120}
            type="primary"
          >
            {t("tuyChinhGiaoDien.sapXepTruong")}
          </Button>
        </Dropdown>
      </>
    );

    const actionMap = {
      dashboard: <ActionDashboard />,
      chiTietPhieuThu: <ActionChiTietPhieuThu />,
      chiTietNhaThuoc: <ActionChiTietDonThuoc />,
    };

    const defaultAction = (
      <Button
        iconHeight={15}
        minWidth={120}
        type="primary"
        onClick={onClickSapXepTruong}
      >
        {t("tuyChinhGiaoDien.sapXepTruong")}
      </Button>
    );

    if (
      !checkRole([ROLES["QUAN_TRI_HE_THONG"].SUA_TUY_CHINH_GIAO_DIEN_PHAN_MEM])
    ) {
      return null;
    }

    if (tab === "tiepDon" || tab === "khamBenh") {
      return renderTiepDonKhamBenh();
    }

    return actionMap[tab] || defaultAction;
  }, [tab]);

  return (
    <Page
      breadcrumb={[
        { title: t("danhMuc.quanTriHeThong"), link: "/quan-tri" },
        {
          title: t("common.tuyChinhGiaoDienPhanMem"),
          link: "/quan-tri/tuy-chinh-giao-dien-phan-mem",
        },
      ]}
      title={t("common.tuyChinhGiaoDienPhanMem")}
      actionRight={renderActionRight}
    >
      <Main bottom={0}>
        <Tabs.Left
          onChange={onChange}
          tabPosition={"left"}
          activeKey={tab}
          tabWidth={220}
          type="card"
          className={`tab-main ${"show-more"}`}
        >
          {listTabs.map((obj, i) => {
            return (
              <Tabs.TabPane
                key={obj.key}
                tab={
                  <div className="flex">
                    {obj?.iconTab}
                    <span className="flex1">{obj?.name}</span>
                  </div>
                }
                disabled={!obj.isShow}
              >
                <Tabs.TabBox
                  fixHeight={false}
                  title={renderTitleTab(obj, i)}
                  //   rightAction={renderRightAction(i)}
                >
                  {obj?.component}
                </Tabs.TabBox>
              </Tabs.TabPane>
            );
          })}
        </Tabs.Left>
      </Main>
    </Page>
  );
};

export default TuyChinhGiaoDienPhanMem;
