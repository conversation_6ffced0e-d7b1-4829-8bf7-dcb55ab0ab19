import React, { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useHistory, useParams, useLocation } from "react-router-dom";
import { Menu, message } from "antd";
import { openInNewTab } from "utils";
import { printJS } from "data-access/print-provider";
import { checkRole } from "lib-utils/role-utils";
import {
  useLayer,
  useLoading,
  useQueryString,
  useStore,
  useThietLap,
  useListAll,
} from "hooks";
import {
  setQueryStringValue,
  transformObjToQueryString,
} from "hooks/useQueryString/queryString";
import DonThuoc from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/ChiDinhDichVu/DonThuoc";
import VatTu from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/ChiDinhDichVu/ChiDinhVatTu";
import ToDieuTri from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri";
import ModalPhieuPHCN from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalPhieuPHCN";
import {
  Button,
  Card,
  Dropdown,
  KhoaThucHien,
  Tabs,
  ThongTinBenhNhan,
  ModalSignPrint,
  withSuspense,
} from "components";
import ThongTinDieuTriPHCN from "../components/ThongTinDieuTriPHCN";
import ModalInPhieu from "../components/ModalInPhieu";
import PhucHoiChucNang from "./PhucHoiChucNang";
import {
  CACHE_KEY,
  DS_TINH_CHAT_KHOA,
  LOAI_DICH_VU,
  MA_BIEU_MAU_EDITOR,
  ROLES,
  LOAI_DANG_KY,
  TRANG_THAI_NB,
  THIET_LAP_CHUNG,
  VI_TRI_PHIEU_IN,
  MAN_HINH_PHIEU_IN,
} from "constants/index";
import { SVG } from "assets";
import { MainPage, Main } from "./styled";
import LuongGiaPHCN from "./LuongGiaPHCN";
import ThongTinKham from "./ThongTinKham";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import HoaChat from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/ChiDinhDichVu/HoaChat";
import { isEmpty } from "lodash";
import ModalChonTieuChi from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ModalChonTieuChi";
import moment from "moment";
import { RightToolBarSinhHieu } from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/TabsList";
import Mau from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/ChiDinhDichVu/Mau";

const VitalSigns = withSuspense(() => import("components/VitalSigns"));

import useListDieuTriKetHop, {
  DIEU_TRI_KET_HOP_OPTIONS,
} from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/hooks/useListDieuTriKetHop";

const ChiTietDieuTriPHCN = () => {
  const { t } = useTranslation();
  const { id, nbDotDieuTriId } = useParams();
  const layerId = useLayer();
  const { showLoading, hideLoading } = useLoading();
  const history = useHistory();
  const [tab] = useQueryString("tab", 0);
  const { state: locationState } = useLocation();
  const [chiTietNguoiBenhTongHop, setChiTietNguoiBenhTongHop] = useState({});

  const refThongTinDieuTriPHCN = useRef(null);
  const refModalInPhieu = useRef(null);
  const refModalPhieuPHCN = useRef(null);
  const refThongTinKham = useRef(null);
  const refModalSignPrint = useRef(null);
  const refModalChonTieuChi = useRef(null);

  const {
    dieuTriPHCN: {
      getChiTietPHCN,
      updateChiTietPHCN,
      inPhieuDieuTri,
      getListAllPhcnKham,
    },
    khoa: { getKhoaTheoTaiKhoan },
    chiDinhKhamBenh: { updateConfigData },
    thietLapChonKho: { getListThietLapChonKhoTheoTaiKhoan },
    toDieuTri: { updateData: updateDataToDieuTri },
    phieuIn: { getListPhieu, showFileEditor, getFilePhieuIn },
    nbLuongGiaPHCN: { clearData: clearDataLuongGia },
    vitalSigns: { updateData: updateDataVitalSigns },
  } = useDispatch();

  const chiTietPHCN = useStore("dieuTriPHCN.chiTietPHCN");
  const listDataLuongGiaPHCN = useStore("nbLuongGiaPHCN.listData", []);
  const activeKey = useStore("toDieuTri.activeKey", null);
  const { listAllDieuTriKetHop, listLoaiPhcnEnums } = useListDieuTriKetHop({
    options: DIEU_TRI_KET_HOP_OPTIONS.CHI_TIET_PHCN,
  });
  const [
    dataHIEN_THI_SINH_HIEU_THEO_KHOA,
    loadFinishHIEN_THI_SINH_HIEU_THEO_KHOA,
  ] = useThietLap(THIET_LAP_CHUNG.HIEN_THI_SINH_HIEU_THEO_KHOA);
  const [dataMA_NHOM_DICH_VU_CAP2_PHCN] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP2_PHCN,
    ""
  );
  const [listAllNhomDichVuCap2] = useListAll("nhomDichVuCap2", {}, true);

  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const tenLoaiPhcn = useMemo(() => {
    const { loaiPhcnId, loai } = chiTietPHCN || {};
    if (
      loai === LOAI_DANG_KY.PHUC_HOI_CHUC_NANG ||
      loai === LOAI_DANG_KY.Y_HOC_CO_TRUYEN
    ) {
      return listLoaiPhcnEnums.find((x) => x.id === loai)?.ten || "";
    }

    return listAllDieuTriKetHop.find((x) => x.id === loaiPhcnId)?.ten || "";
  }, [chiTietPHCN, listAllDieuTriKetHop, listLoaiPhcnEnums]);

  const nhomDichVuCap2IdMemo = useMemo(() => {
    if (
      dataMA_NHOM_DICH_VU_CAP2_PHCN &&
      listAllNhomDichVuCap2 &&
      listAllNhomDichVuCap2.length > 0
    ) {
      let listMa = dataMA_NHOM_DICH_VU_CAP2_PHCN.split(",");
      listMa = listMa ? listMa.map((x) => x?.trim()) : [];

      const selectedNhom = (listAllNhomDichVuCap2 || [])
        .filter((x) => listMa?.includes(x.ma))
        .map((x) => x.id);

      return selectedNhom || [];
    } else {
      return [];
    }
  }, [dataMA_NHOM_DICH_VU_CAP2_PHCN, listAllNhomDichVuCap2]);

  const isPhcn = useMemo(() => {
    return chiTietPHCN?.loai === LOAI_DANG_KY.PHUC_HOI_CHUC_NANG;
  }, [chiTietPHCN?.loai]);

  useEffect(() => {
    getKhoaTheoTaiKhoan({
      dsTinhChatKhoa: DS_TINH_CHAT_KHOA.NOI_TRU,
      page: "",
      size: "",
      ative: true,
    });
  }, []);

  useEffect(() => {
    if (id) {
      getChiTietPHCN(id);
    }
  }, [id]);

  useEffect(() => {
    if (
      activeKey == "7" &&
      chiTietPHCN.id &&
      loadFinishHIEN_THI_SINH_HIEU_THEO_KHOA
    ) {
      updateDataVitalSigns({
        configData: {
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.NOI_TRU, //Hiển thị danh sách sinh hiệu của NB loại = nội trú, phẫu thuật
          chiDinhTuDichVuId: chiTietPHCN.id,
          dsChiDinhTuLoaiDichVu: [LOAI_DICH_VU.NOI_TRU, LOAI_DICH_VU.PHCN],
          hienThiTheoKhoa: !!dataHIEN_THI_SINH_HIEU_THEO_KHOA.eval(),
        },
      });
    }
  }, [
    activeKey,
    chiTietPHCN.id,
    dataHIEN_THI_SINH_HIEU_THEO_KHOA,
    loadFinishHIEN_THI_SINH_HIEU_THEO_KHOA,
  ]);

  const locSoPhieuTheoLoaiPHCN = (data, loai) => {
    if (loai === 20) {
      return data.filter((x) => x.ma !== "P141");
    }
    if (loai === 10) {
      return data.filter((x) => x.ma !== "P442");
    }

    return data;
  };

  useEffect(() => {
    if (chiTietPHCN.nbDotDieuTriId) {
      nbDotDieuTriProvider
        .getByIdTongHop(chiTietPHCN.nbDotDieuTriId)
        .then((s) => {
          setChiTietNguoiBenhTongHop(s?.data || {});
        });
      getListPhieu({
        nbDotDieuTriId: chiTietPHCN.nbDotDieuTriId,
        maManHinh: MAN_HINH_PHIEU_IN.CHI_TIET_PHCN,
        maViTri: VI_TRI_PHIEU_IN.CHI_TIET_PHCN.IN_PHIEU,
      }).then((listPhieu) => {
        let phieuKhamPhcn = listPhieu.find((i) => i.ma === "P394");
        let listPhieus = locSoPhieuTheoLoaiPHCN(listPhieu, chiTietPHCN.loai);

        setState({
          listPhieu: listPhieus,
          ...(phieuKhamPhcn && { phieuKhamPhcn }),
        });
      });
    }
  }, [chiTietPHCN]);

  useEffect(() => {
    if (tab) {
      onChangeTab(tab);
    }
  }, [tab]);

  const isReadOnly = useMemo(() => {
    //Hủy đăng ký 20, Hoàn thành 40
    return [20, 40].includes(chiTietPHCN?.trangThai);
  }, [chiTietPHCN?.trangThai]);

  useEffect(() => {
    if (chiTietNguoiBenhTongHop?.id && id) {
      updateConfigData({
        configData: {
          chiDinhTuDichVuId: id,
          nbDotDieuTriId: nbDotDieuTriId,
          nbThongTinId: chiTietNguoiBenhTongHop.nbThongTinId,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHCN,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.PHCN,
          khoaChiDinhId: state.khoaLamViec?.id,
          thongTinNguoiBenh: chiTietNguoiBenhTongHop,
          loaiDoiTuongId: chiTietNguoiBenhTongHop.loaiDoiTuongId,
          doiTuongKcb: chiTietNguoiBenhTongHop.doiTuongKcb,
          canLamSang: false,
          phongThucHienId: chiTietNguoiBenhTongHop?.phongId,
        },
      });
    }
  }, [chiTietNguoiBenhTongHop, id, state.khoaLamViec]);

  useEffect(() => {
    if (tenLoaiPhcn) {
      document.title = tenLoaiPhcn;
    }
  }, [tenLoaiPhcn]);

  const onChangeTab = (e) => {
    setQueryStringValue("tab", e);
    updateDataToDieuTri({ activeKey: e });
    let loaiDichVu = LOAI_DICH_VU.THUOC;
    if (e === "3") {
      loaiDichVu = LOAI_DICH_VU.VAT_TU;
    }
    if (e === "4") {
      loaiDichVu = LOAI_DICH_VU.HOA_CHAT;
    }
    if (e === "8") {
      loaiDichVu = LOAI_DICH_VU.CHE_PHAM_MAU;
    }
    let payload = {
      khoaNbId: chiTietNguoiBenhTongHop?.khoaNbId,
      khoaChiDinhId: state.khoaLamViec?.id,
      doiTuong: chiTietNguoiBenhTongHop?.doiTuong,
      loaiDoiTuongId: chiTietNguoiBenhTongHop?.loaiDoiTuongId,
      capCuu: chiTietNguoiBenhTongHop?.capCuu,
      phongId: chiTietNguoiBenhTongHop?.phongId,
      noiTru: true,
      canLamSang: false,
      loaiDichVu: loaiDichVu,
    };
    getListThietLapChonKhoTheoTaiKhoan({ ...payload });
  };

  const listTabs = [
    {
      name: tenLoaiPhcn,
      component: (
        <PhucHoiChucNang
          tenLoaiPhcn={tenLoaiPhcn}
          nhomDichVuCap2IdMemo={nhomDichVuCap2IdMemo}
        />
      ),
      iconTab: <SVG.IcChiDinhDichVu />,
      isShow: true,
      index: "0",
    },
    ...(checkRole([ROLES["QUAN_LY_NOI_TRU"].TO_DIEU_TRI])
      ? [
          {
            name: t("phcn.toDieuTriChiDinh"),
            component: (
              <ToDieuTri
                khoaLamViec={state.khoaLamViec}
                isReadonly={isReadOnly}
                id={nbDotDieuTriId}
                loaiToDieuTri={12}
                phcnId={chiTietPHCN?.id}
                loaiPhcnId={chiTietPHCN?.loaiPhcnId}
                loaiPhcn={chiTietPHCN?.loai}
              />
            ),
            iconTab: <SVG.IcToDieuTri />,
            isShow: true,
            index: "1",
          },
        ]
      : []),
    {
      name: t("common.thuoc"),
      component: (
        <DonThuoc
          isReadonly={isReadOnly}
          layerId={activeKey === "2" ? layerId : null}
        />
      ),
      iconTab: <SVG.IcDonThuoc color={"var(--color-gray-primary)"} />,
      isShow: true,
      index: "2",
    },
    {
      name: t("common.vatTu"),
      component: (
        <VatTu
          isReadonly={isReadOnly}
          layerId={activeKey === "3" ? layerId : null}
        />
      ),
      iconTab: <SVG.IcVatTu />,
      isShow: true,
      index: "3",
    },
    {
      name: t("danhMuc.hoaChat"),
      component: (
        <HoaChat
          chiDinhTuLoaiDichVu={LOAI_DICH_VU.PHCN}
          chiTietPHCN={chiTietPHCN}
          isReadonly={isReadOnly}
          layerId={activeKey === "4" ? layerId : null}
        />
      ),
      iconTab: <SVG.IcHoaChat />,
      isShow: true,
      index: "4",
    },
    ...(checkRole([ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_TAB_LUONG_GIA])
      ? [
          {
            name: t("phcn.luongGia.title"),
            component: <LuongGiaPHCN />,
            iconTab: <SVG.IcChiDinhDichVu />,
            isShow: true,
            index: "5",
          },
        ]
      : []),
    ...(checkRole([ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_TAB_KHAM])
      ? [
          {
            name: t("phcn.kham.thongTinKhamChiDinhPHCN"),
            component: (
              <ThongTinKham
                ref={refThongTinKham}
                phieuKhamPhcn={state.phieuKhamPhcn}
              />
            ),
            iconTab: <SVG.IcChiDinhDichVu />,
            isShow: true,
            index: "6",
          },
        ]
      : []),
    ...(checkRole([ROLES["QUAN_LY_NOI_TRU"].DO_CHI_SO_SONG])
      ? [
          {
            name: t("title.sinhHieu"),
            component: (
              <VitalSigns
                isReadonly={isReadOnly}
                isEdit={
                  chiTietPHCN.trangThai !== TRANG_THAI_NB.CHO_TIEP_NHAN_VAO_KHOA
                }
                khoaLamViec={state.khoaLamViec}
                rightToolbar={<RightToolBarSinhHieu />}
                nbDotDieuTriId={nbDotDieuTriId}
              />
            ),
            iconTab: <SVG.IcChiSoSong />,
            isShow: true,
            index: "7",
          },
        ]
      : []),
    {
      name: t("quanLyNoiTru.dvNoiTru.mau"),
      component: (
        <Mau
          chiDinhTuLoaiDichVu={LOAI_DICH_VU.PHCN}
          chiDinhTuDichVuId={chiTietPHCN?.id}
          chiTietPHCN={chiTietPHCN}
          isReadonly={isReadOnly}
          layerId={activeKey === "8" ? layerId : null}
          khoaLamViec={state.khoaLamViec}
        />
      ),
      iconTab: <SVG.IcMau />,
      isShow: true,
      index: "8",
    },
  ];

  const onSave = () => {
    const data = refThongTinDieuTriPHCN.current
      ? refThongTinDieuTriPHCN.current.getData()
      : {};
    if (isEmpty(data)) {
      return;
    }

    const payload = {
      ...data,
      id: id,
    };

    showLoading();
    updateChiTietPHCN(payload)
      .then(() => {
        getChiTietPHCN(id);
      })
      .catch(() => {})
      .finally(() => {
        hideLoading();
      });
  };

  const onPrintPhieuDieuTriPHCN = () => {
    refModalInPhieu.current &&
      refModalInPhieu.current.show(
        { title: t("phcn.tieuChiChonInPhieu") },
        async ({ tuThoiGian, denThoiGian }) => {
          showLoading();
          try {
            await inPhieuDieuTri({
              id,
              tuThoiGian,
              denThoiGian,
            });
          } catch (error) {
            message.error(error);
          } finally {
            hideLoading();
          }
        }
      );
  };

  const onPrintPhieu = (item) => async () => {
    if (item.type == "editor") {
      const lichSuKyId = item?.dsSoPhieu?.length
        ? item?.dsSoPhieu[0].lichSuKyId
        : "";
      if (item.ma === "P141" || item.ma === "P442") {
        refModalPhieuPHCN.current &&
          refModalPhieuPHCN.current.show(item, (values) => {
            showFileEditor({
              phieu: item,
              nbDotDieuTriId,
              id: nbDotDieuTriId,
              ma: item.ma,
              maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                : "",
              tuThoiGian: values.tuThoiGianThucHien,
              denThoiGian: values.denThoiGianThucHien,
              lichSuKyId,
              loai: chiTietPHCN.loai,
            });
          });
      } else {
        let mhParams = {};
        //kiểm tra phiếu ký số
        if (checkIsPhieuKySo(item)) {
          //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
          mhParams = {
            nbDotDieuTriId: chiTietPHCN.nbDotDieuTriId,
            maManHinh: MAN_HINH_PHIEU_IN.CHI_TIET_PHCN,
            maViTri: VI_TRI_PHIEU_IN.CHI_TIET_PHCN.IN_PHIEU,
            kySo: true,
            maPhieuKy: item.ma,
          };
        }

        showFileEditor({
          phieu: item,
          nbDotDieuTriId,
          lichSuKyId,
          id: chiTietPHCN.id,
          maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
            ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
            : "",
          mhParams,
        });
      }
    } else {
      if (item.ma === "P394") {
        let _dsTieuChi = await getListAllPhcnKham({
          nbDotDieuTriId: chiTietPHCN?.nbDotDieuTriId,
        });
        if (!_dsTieuChi.length) {
          message.error(t("phieuIn.khongTonTaiPhieuIn"));
          return;
        }
        _dsTieuChi = _dsTieuChi.map((x) => ({
          id: x.id,
          ten: `${item.ten} - ${moment(x.thoiGianTao).format("DD/MM/YYYY")}`,
        }));

        refModalChonTieuChi &&
          refModalChonTieuChi.current.show(
            { data: _dsTieuChi },
            async (idTieuChi) => {
              if (checkIsPhieuKySo(item)) {
                refModalSignPrint.current &&
                  refModalSignPrint.current.showToSign({
                    phieuKy: item,
                    payload: {
                      nbDotDieuTriId: chiTietPHCN?.nbDotDieuTriId,
                      chiDinhTuDichVuId: idTieuChi,
                      maManHinh: MAN_HINH_PHIEU_IN.CHI_TIET_PHCN,
                      maViTri: VI_TRI_PHIEU_IN.CHI_TIET_PHCN.IN_PHIEU,
                    },
                  });
              } else {
                try {
                  showLoading();
                  const { finalFile, dsPhieu } = await getFilePhieuIn({
                    listPhieus: [item],
                    chiDinhTuDichVuId: idTieuChi,
                    showError: true,
                  });
                  if ((dsPhieu || []).every((x) => x?.loaiIn == 20)) {
                    openInNewTab(finalFile);
                  } else {
                    printJS({
                      printable: finalFile,
                      type: "pdf",
                    });
                  }
                } catch (error) {
                  console.error(error);
                } finally {
                  hideLoading();
                }
              }
            }
          );
      } else {
        if (checkIsPhieuKySo(item)) {
          refModalSignPrint.current &&
            refModalSignPrint.current.showToSign({
              phieuKy: item,
              payload: {
                nbDotDieuTriId: chiTietPHCN?.nbDotDieuTriId,
                chiDinhTuDichVuId: chiTietPHCN?.id,
                dsChiDinhTuLoaiDichVu: [LOAI_DICH_VU.PHCN],
                dsNhomDichVuCap2Id: nhomDichVuCap2IdMemo,
                maManHinh: MAN_HINH_PHIEU_IN.CHI_TIET_PHCN,
                maViTri: VI_TRI_PHIEU_IN.CHI_TIET_PHCN.IN_PHIEU,
              },
            });
        } else {
          try {
            showLoading();
            const { finalFile, dsPhieu } = await getFilePhieuIn({
              listPhieus: [item],
              showError: true,
              nbDotDieuTriId,
              chiDinhTuDichVuId: chiTietPHCN?.id,
              dsChiDinhTuLoaiDichVu: [LOAI_DICH_VU.PHCN],
              dsNhomDichVuCap2Id: nhomDichVuCap2IdMemo,
            });
            if ((dsPhieu || []).every((x) => x?.loaiIn == 20)) {
              openInNewTab(finalFile);
            } else {
              printJS({
                printable: finalFile,
                type: "pdf",
              });
            }
          } catch (error) {
            console.error(error);
          } finally {
            hideLoading();
          }
        }
      }
    }
  };

  const menu = useMemo(() => {
    return (
      <Menu
        items={(state.listPhieu || []).map((item, index) => {
          if (item.ma == "P145") {
            return {
              key: index + "",
              label: (
                <div onClick={onPrintPhieuDieuTriPHCN}>
                  {item.ten || item.tenBaoCao}
                </div>
              ),
            };
          }
          return {
            key: index,
            label: (
              <a onClick={onPrintPhieu(item)}>{item.ten || item.tenBaoCao}</a>
            ),
          };
        })}
      />
    );
  }, [state?.listPhieu]);

  const onChangeKhoaThucHien = (khoaLamViec) => {
    setState({ khoaLamViec });
  };

  const renderRightAction = (index) => {
    switch (index) {
      case "5":
        if (listDataLuongGiaPHCN.length > 0) {
          return (
            <>
              <Button
                type="success"
                rightIcon={<SVG.IcAdd />}
                onClick={() => {
                  clearDataLuongGia();

                  history.push(
                    `/phuc-hoi-chuc-nang/luong-gia-phcn/${nbDotDieuTriId}/${id}/them-moi`
                  );
                }}
              >
                {t("common.themMoiF1")}
              </Button>
            </>
          );
        }
        break;

      case "6":
        return (
          <>
            <Button
              type="success"
              rightIcon={<SVG.IcAdd />}
              onClick={() => {
                history.push(
                  `/phuc-hoi-chuc-nang/phcn-kham/${nbDotDieuTriId}/${id}/them-moi`
                );
              }}
            >
              {t("common.themMoiF1")}
            </Button>
          </>
        );

      default:
        break;
    }

    return null;
  };

  const refreshPhieu = () => {
    getListPhieu({
      nbDotDieuTriId: chiTietPHCN.nbDotDieuTriId,
      maManHinh: MAN_HINH_PHIEU_IN.CHI_TIET_PHCN,
      maViTri: VI_TRI_PHIEU_IN.CHI_TIET_PHCN.IN_PHIEU,
    }).then((listPhieu) => {
      let phieuKhamPhcn = listPhieu.find((i) => i.ma === "P394");
      let listPhieus = locSoPhieuTheoLoaiPHCN(listPhieu, chiTietPHCN.loai);
      setState({
        listPhieu: listPhieus,
        ...(phieuKhamPhcn && { phieuKhamPhcn }),
      });
    });
  };
  return (
    <MainPage
      breadcrumb={[
        {
          link: "/phuc-hoi-chuc-nang",
          title: tenLoaiPhcn,
        },
        {
          link:
            "/phuc-hoi-chuc-nang/ds-dieu-tri-phcn" +
            transformObjToQueryString(locationState),
          title: t("phcn.dieuTriPhcnTen", { ten: tenLoaiPhcn }),
        },
        {
          link: window.location.pathname,
          title: t("phcn.chiTietDieuTriPhcnTen", { ten: tenLoaiPhcn }),
        },
      ]}
      title={t("phcn.dieuTriPhcnTen", { ten: tenLoaiPhcn })}
      titleRight={
        <KhoaThucHien
          cacheKey={CACHE_KEY.DATA_KHOA_LAM_VIEC_PHCN}
          dsTinhChatKhoa={DS_TINH_CHAT_KHOA.NOI_TRU}
          onChange={onChangeKhoaThucHien}
          type={1}
        />
      }
      actionLeft={
        <Button
          minWidth={100}
          onClick={() => {
            history.push(
              "/phuc-hoi-chuc-nang/ds-dieu-tri-phcn" +
                transformObjToQueryString(locationState)
            );
          }}
        >
          {t("common.quayLai")}
        </Button>
      }
      actionRight={
        <>
          <Dropdown overlay={menu} trigger={["click"]}>
            <Button
              minWidth={100}
              rightIcon={<SVG.IcPrint />}
              onClick={refreshPhieu}
            >
              {t("common.inPhieu")}
            </Button>
          </Dropdown>

          <Button
            minWidth={100}
            type="primary"
            iconHeight={15}
            onClick={onSave}
            rightIcon={<SVG.IcSave />}
          >
            {t("common.luu")}
          </Button>
        </>
      }
    >
      <Main>
        <ThongTinBenhNhan
          nbDotDieuTriId={nbDotDieuTriId}
          isShowDeNghiTamUng={true}
        />

        <Card top={16} bottom={16}>
          <ThongTinDieuTriPHCN
            tenLoaiPhcn={tenLoaiPhcn}
            ref={refThongTinDieuTriPHCN}
            isPhcn={isPhcn}
          />
        </Card>

        <Card top={16} bottom={0} className={"main-body-content"}>
          <Tabs.Left
            activeKey={activeKey || "0"}
            tabPosition={"left"}
            tabWidth={220}
            type="card"
            className={`tab-main ${
              state.collapse ? "collapse-tab" : "show-more"
            }`}
            onChange={onChangeTab}
          >
            {listTabs.map((obj, idx) => {
              return (
                <Tabs.TabPane
                  key={obj.index} // dùng index để xác định được tab, ko dùng idx
                  tab={
                    <div>
                      {obj?.iconTab}
                      {obj?.name}
                    </div>
                  }
                  disabled={!obj.isShow}
                >
                  <Tabs.TabBox
                    fixHeight={false}
                    title={obj?.name}
                    rightAction={renderRightAction(obj?.index)}
                  >
                    {obj?.component}
                  </Tabs.TabBox>
                </Tabs.TabPane>
              );
            })}
          </Tabs.Left>
        </Card>
      </Main>

      <ModalInPhieu ref={refModalInPhieu} />
      <ModalPhieuPHCN ref={refModalPhieuPHCN} />
      <ModalSignPrint ref={refModalSignPrint} />
      <ModalChonTieuChi ref={refModalChonTieuChi} />
    </MainPage>
  );
};

export default ChiTietDieuTriPHCN;
