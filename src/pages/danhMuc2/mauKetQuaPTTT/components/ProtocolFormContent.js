import React, { useMemo, useCallback, forwardRef, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Form, Input, Radio } from "antd";
import { Checkbox, Button } from "components";
import { DeboundInput } from "components/editor/config";
import { SVG } from "assets";
import styled, { css, createGlobalStyle } from "styled-components";
import ModalDanhDauThatBai from "./ModalDanhDauThatBai";

const isFieldEmpty = (value) => {
  if (value === null || value === undefined) return true;
  if (typeof value === "boolean") return !value;
  if (typeof value === "string") return value.trim() === "";
  return false;
};

const GlobalPrintStyle = createGlobalStyle`
  @media print {
    .protocol-field-empty {
      display: none !important;
    }
  }
`;

const CustomRadio = forwardRef(({ softDisabled, onChange, ...props }, ref) => {
  const handleChange = (e) => {
    if (softDisabled) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    onChange?.(e);
  };

  return <Radio {...props} onChange={handleChange} ref={ref} />;
});

const FormItem = styled(Form.Item)`
  margin-bottom: 0px;

  &.hidden-form-item {
    .ant-form-item-control-input {
      display: none;
    }
  }
  .protocol-checkbox-wrapper {
    margin-left: 20px;
  }
  .ant-form-item-explain-error {
    margin-left: 20px;
  }
  &.protocol-comment-item {
    flex: 1;
  }

  @media print {
    &.protocol-field-empty {
      display: none !important;
    }
  }
`;

const FieldRenderContainer = styled.div`
  margin-bottom: 0px;
  position: relative;

  ${({ $hasComment }) =>
    $hasComment &&
    css`
      display: flex;
      textarea {
        top: 3px;
      }
    `}

  @media print {
    &.protocol-field-empty {
      display: none !important;
    }
  }
`;

// Button "Thất bại" - Khi click sẽ mở modal đánh dấu thất bại
const FailureButton = styled(Button)`
  margin-left: 8px;
  min-width: 80px;
  height: 28px;
  font-size: 12px;
  @media print {
    display: none;
  }

  &.failed {
    background-color: #ff4d4f;
    border-color: #ff4d4f;
    color: white;

    &:hover {
      background-color: #ff7875;
      border-color: #ff7875;
    }
  }
`;

// Hiển thị thông tin thất bại - Thay thế button khi đã đánh dấu thất bại
const FailureInfo = styled.div`
  margin-top: 4px;
  margin-left: 20px;
  padding: 8px 12px;
  background-color: #fff2f0;
  border-left: 3px solid #ff4d4f;
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
  gap: 8px;

  .failure-icon {
    color: #ff4d4f;
    margin-top: 2px;
  }

  .failure-content {
    flex: 1;

    .failure-label {
      font-weight: 600;
      color: #ff4d4f;
      font-size: 13px;
      margin-bottom: 4px;
    }

    .failure-reason {
      color: rgba(0, 0, 0, 0.85);
      font-size: 12px;
      line-height: 1.5;
      white-space: pre-wrap;
      word-break: break-word;

      span:first-child {
        font-weight: 500;
      }
    }
  }

  @media print {
    background-color: white;
    border: none;
    .failure-icon {
      display: none;
    }
    .failure-content {
      .failure-label {
        color: #000000d9;
        display: inline;
        &::after {
          content: ". ";
          display: inline;
        }
      }

      .failure-reason {
        display: inline;
      }
    }
  }
`;

const ParentChildFormItem = ({
  children,
  parentItem,
  formValues,
  t,
  isAfterFailedStep,
  ...formItemProps
}) => {
  // Tạo field name cho parent group validation
  const parentFieldName = `parent_${parentItem?.id}_validation`;

  // Tạo danh sách dependencies từ child fields
  const childFieldNames = useMemo(() => {
    if (!parentItem?.children) return [];
    return parentItem.children.map((child) => `field_${child.id}`);
  }, [parentItem?.children]);

  // Custom validator cho parent group
  const validateParentGroup = () => {
    // Skip validation nếu nằm sau bước thất bại
    if (isAfterFailedStep) {
      return Promise.resolve();
    }

    if (!parentItem?.batBuoc || !parentItem?.children) {
      return Promise.resolve();
    }

    // Kiểm tra có ít nhất một child được chọn
    const hasSelectedChild = parentItem.children.some((child) => {
      const fieldName = `field_${child.id}`;
      const fieldValue = formValues?.[fieldName];

      return (
        fieldValue === true ||
        (fieldValue && fieldValue.trim && fieldValue.trim() !== "")
      );
    });

    if (!hasSelectedChild) {
      return Promise.reject(new Error(t("danhMuc.vuiLongChonItNhatMotGiaTri")));
    }

    return Promise.resolve();
  };

  if (!parentItem?.batBuoc || !parentItem?.children) {
    return <div>{children}</div>;
  }

  return (
    <div>
      {children}
      <FormItem
        name={parentFieldName}
        dependencies={childFieldNames}
        rules={[{ validator: validateParentGroup }]}
        className="hidden-form-item"
        {...formItemProps}
      >
        <Input hidden />
      </FormItem>
    </div>
  );
};

const ProtocolFormContent = ({
  protocolId,
  disabled = false,
  formValues,
  onSingleSelection,
  listProtocolChiTiet,
  hideEmptyFieldsOnPrint = false,
  onFailureUpdate,
}) => {
  const { t } = useTranslation();
  const refModalDanhDauThatBai = useRef(null);
  const failureDataMap = useMemo(() => {
    return listProtocolChiTiet?.reduce((acc, item) => {
      acc.set(item.id, {
        thucHienThatBai: formValues?.[`thucHienThatBai_${item.id}`],
        lyDoThatBai: formValues?.[`lyDoThatBai_${item.id}`],
      });
      return acc;
    }, new Map());
  }, [listProtocolChiTiet, formValues]);

  // Tìm failure data cho mỗi protocol item
  const getFailureData = (protocolChiTietId) => {
    return failureDataMap.get(protocolChiTietId);
  };

  const areAllChildrenEmpty = (item) => {
    if (!item.children || item.children.length === 0) return false;

    return item.children.every((child) => {
      const fieldName = `field_${child.id}`;
      const commentFieldName = `comment_${child.id}`;
      const thucHienThatBaiFieldName = `thucHienThatBai_${child.id}`;
      const lyDoThatBaiFieldName = `lyDoThatBai_${child.id}`;
      const fieldValue = formValues?.[fieldName];
      const commentValue = formValues?.[commentFieldName];
      const thucHienThatBaiValue = formValues?.[thucHienThatBaiFieldName];
      const lyDoThatBaiValue = formValues?.[lyDoThatBaiFieldName];

      const isMainFieldEmpty = isFieldEmpty(fieldValue);
      const isCommentEmpty = isFieldEmpty(commentValue);
      const isFailureFieldEmpty = !(
        thucHienThatBaiValue && !isFieldEmpty(lyDoThatBaiValue)
      );
      if (child.children && child.children.length > 0) {
        return areAllChildrenEmpty(child);
      }

      return isMainFieldEmpty && isCommentEmpty && isFailureFieldEmpty;
    });
  };

  const buildHierarchicalData = (data) => {
    if (!data || !Array.isArray(data)) return [];

    const flatItems = data.map((item) => {
      const ma = item.protocolTenTruong?.ma || "";
      const level = ma.split(".").length;
      let loaiHienThi = !item?.protocolTenTruong?.tenTruongChaId
        ? 10
        : item.loaiHienThi;

      return {
        ...item,
        key: item.id,
        level,
        children: [],
        ma,
        loaiHienThi,
        orgLoaiHienThi: item.loaiHienThi,
      };
    });

    const rootItems = [];
    const itemMap = new Map();

    flatItems.forEach((item) => {
      itemMap.set(item.ma, item);
    });

    flatItems.forEach((item) => {
      const maParts = item.ma.split(".");
      if (maParts.length > 1) {
        const parentMa = maParts.slice(0, -1).join(".");
        const parent = itemMap.get(parentMa);

        if (parent) {
          item.loaiHienThi = parent.orgLoaiHienThi ?? parent.loaiHienThi;
          parent.children.push(item);
        } else {
          rootItems.push(item);
        }
      } else {
        rootItems.push(item);
      }
    });

    return rootItems;
  };

  const hierarchicalData = useMemo(() => {
    return buildHierarchicalData(listProtocolChiTiet || []);
  }, [listProtocolChiTiet]);

  const flatData = useMemo(() => {
    const flattenTree = (nodes) => {
      const result = [];
      const traverse = (node) => {
        result.push(node);
        if (Array.isArray(node.children) && node.children.length > 0) {
          node.children.forEach(traverse);
        }
      };
      nodes.forEach(traverse);
      return result;
    };
    return flattenTree(hierarchicalData);
  }, [hierarchicalData]);

  const treeMetadata = useMemo(() => {
    if (!hierarchicalData)
      return { nodeMap: new Map(), siblingsMap: new Map() };

    const nodeMap = new Map(); // Map<nodeId, node>
    const siblingsMap = new Map(); // Map<nodeId, { siblings: [], index: number }>

    const buildMetadata = (items, parentId = null) => {
      items.forEach((item, index) => {
        nodeMap.set(item.id, item);

        // Lưu thông tin về siblings và vị trí
        siblingsMap.set(item.id, {
          siblings: items,
          index: index,
          parentId: parentId,
        });

        // Đệ quy vào children
        if (item.children && item.children.length > 0) {
          buildMetadata(item.children, item.id);
        }
      });
    };

    buildMetadata(hierarchicalData);

    return { nodeMap, siblingsMap };
  }, [hierarchicalData]);

  const collectAllDescendants = useCallback((node, result = new Set()) => {
    if (!node.children || node.children.length === 0) return result;

    node.children.forEach((child) => {
      result.add(child.id);
      collectAllDescendants(child, result);
    });

    return result;
  }, []);

  const affectedItemIds = useMemo(() => {
    if (!formValues || !treeMetadata.siblingsMap.size) return new Set();

    const affected = new Set();

    // Duyệt qua tất cả items để tìm những item thất bại
    treeMetadata.siblingsMap.forEach((metadata, itemId) => {
      const thucHienThatBai = formValues[`thucHienThatBai_${itemId}`];

      if (thucHienThatBai) {
        const { siblings, index } = metadata;

        // Đánh dấu tất cả siblings của item này và tất cả siblings sau item này
        for (let i = index; i < siblings.length; i++) {
          const sibling = siblings[i];
          affected.add(sibling.id);

          // Thu thập tất cả descendants của sibling
          collectAllDescendants(sibling, affected);
        }
      }
    });

    return affected;
  }, [formValues, treeMetadata, collectAllDescendants]);

  const isAfterFailedStep = (item) => {
    return affectedItemIds.has(item.id);
  };

  const handleSingleSelection = (item, checked) => {
    const siblings =
      flatData?.filter(
        (sibling) =>
          sibling.protocolTenTruong?.tenTruongChaId ===
            item.protocolTenTruong?.tenTruongChaId && sibling.loaiHienThi === 20
      ) || [];

    const newValues = { ...formValues };

    siblings.forEach((sibling) => {
      newValues[`field_${sibling.id}`] = false;
    });

    if (checked) {
      newValues[`field_${item.id}`] = true;
    }

    onSingleSelection?.(newValues);
  };

  // Handler cho button thất bại - Mở modal đánh dấu thất bại
  const handleShowFailureModal = (item) => {
    if (!refModalDanhDauThatBai?.current) return;

    refModalDanhDauThatBai.current.show(
      {
        id: item.id,
        ten: item.protocolTenTruong?.ten || "",
      },
      (data) => {
        // Callback khi confirm - cập nhật failure data
        if (onFailureUpdate) {
          onFailureUpdate(item.id, data);
        }
      }
    );
  };

  const renderFormField = (item) => {
    const fieldName = `field_${item.id}`;
    const commentFieldName = `comment_${item.id}`;

    const hasComment = item.ghiChu;
    const showParentAsterisk =
      item.batBuoc && item.children && !isAfterFailedStep(item);
    const failureData = getFailureData(item.id);
    const hasFailed = failureData?.thucHienThatBai;
    // Hiển thị button "Thất bại" chỉ khi item có thucHienThatBai=true và không bị disabled
    const showFailureButton = item.thucHienThatBai && !disabled && !hasFailed;

    let emptyClassName = "";

    if (hideEmptyFieldsOnPrint) {
      const fieldValue = formValues?.[fieldName];
      const commentValue = formValues?.[commentFieldName];

      const isMainFieldEmpty = isFieldEmpty(fieldValue);
      const isCommentFieldEmpty = isFieldEmpty(commentValue);
      const isFailureFieldEmpty = !(
        hasFailed && !isFieldEmpty(failureData?.lyDoThatBai)
      );

      // Nếu là parent item thì kiểm tra xem tất cả children có rỗng không
      const hasChildren = item.children && item.children.length > 0;
      const isParentWithEmptyChildren =
        hasChildren && areAllChildrenEmpty(item);

      // Apply empty class nếu hideEmptyFieldsOnPrint là true
      // Logic:
      // - Nếu là parent (có children): chỉ ẩn khi TẤT CẢ children rỗng
      // - Nếu không phải parent: ẩn khi field rỗng
      const shouldHideOnPrint = hasChildren
        ? isParentWithEmptyChildren // Parent: chỉ ẩn khi tất cả children rỗng
        : isMainFieldEmpty && isCommentFieldEmpty && isFailureFieldEmpty; // Field thường: ẩn khi rỗng
      emptyClassName = shouldHideOnPrint ? "protocol-field-empty" : "";
    }

    const labelStyle = {
      fontWeight: item.level === 1 ? "bold" : "normal",
      fontSize: item.level === 1 ? "16px" : "14px",
      marginLeft: 20,
    };

    const label = (
      <span style={{ ...labelStyle, display: "flex", alignItems: "center" }}>
        <span>
          {item.protocolTenTruong?.ten}
          {showParentAsterisk && (
            <span style={{ color: "red", marginLeft: "4px" }}>*</span>
          )}
        </span>
        {showFailureButton && (
          <FailureButton
            size="small"
            type="default"
            onClick={() => handleShowFailureModal(item)}
          >
            {t("pttt.thatBai")}
          </FailureButton>
        )}
      </span>
    );

    // Render failure info component - Hiển thị thay thế button khi đã đánh dấu thất bại
    const renderFailureInfo = (id) => {
      return (
        <>
          <Form.Item name={`thucHienThatBai_${id}`} hidden>
            <Input hidden />
          </Form.Item>
          <Form.Item name={`lyDoThatBai_${id}`} hidden>
            <Input hidden />
          </Form.Item>
          <FormItem
            noStyle
            shouldUpdate={(prevValues, currentValues) => {
              return (
                prevValues[`thucHienThatBai_${id}`] !==
                  currentValues[`thucHienThatBai_${id}`] ||
                prevValues[`lyDoThatBai_${id}`] !==
                  currentValues[`lyDoThatBai_${id}`]
              );
            }}
          >
            {({ getFieldValue }) => {
              const thucHienThatBai = getFieldValue(`thucHienThatBai_${id}`);
              const lyDoThatBai = getFieldValue(`lyDoThatBai_${id}`);
              if (!thucHienThatBai) return null;
              return (
                <FailureInfo>
                  <SVG.IcWarning className="failure-icon" />
                  <div className="failure-content">
                    <div className="failure-label">
                      {t("pttt.thucHienThatBai")}
                    </div>
                    <div className="failure-reason">
                      <span className="failure-reason-label">
                        {t("pttt.lyDo")}:{" "}
                      </span>
                      <span>{lyDoThatBai}</span>
                    </div>
                  </div>
                </FailureInfo>
              );
            }}
          </FormItem>
        </>
      );
    };

    switch (item.loaiHienThi) {
      case 10: // Chữ - TextField hoặc chỉ label
        if (hasComment) {
          // Hiển thị TextField inline với label "Chi tiết" khi có ghiChu = true
          return (
            <div key={item.id}>
              <FieldRenderContainer
                $hasComment={hasComment}
                className={emptyClassName}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "flex-start",
                    gap: "16px",
                    width: "100%",
                  }}
                >
                  <div style={{ flex: 1 }}>
                    <FormItem
                      label={label}
                      name={fieldName}
                      $hasComment={hasComment}
                    >
                      <DeboundInput
                        type="multipleline"
                        readOnly={disabled}
                        markSpanRow={true}
                      />
                    </FormItem>
                  </div>
                  <div style={{ flex: 1 }}>
                    <FormItem
                      label={""}
                      name={commentFieldName}
                      $hasComment={hasComment}
                      className="protocol-comment-item"
                    >
                      <DeboundInput
                        type="multipleline"
                        readOnly={disabled}
                        markSpanRow={true}
                      />
                    </FormItem>
                  </div>
                </div>
              </FieldRenderContainer>
              {renderFailureInfo(item.id)}
            </div>
          );
        } else {
          // Chỉ hiển thị label khi ghiChu = false
          return (
            <div key={item.id}>
              <FieldRenderContainer
                $hasComment={hasComment}
                className={emptyClassName}
              >
                <div
                  style={{
                    ...labelStyle,
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <span>
                    {item.protocolTenTruong?.ten}
                    {showParentAsterisk && (
                      <span style={{ color: "red", marginLeft: "4px" }}>*</span>
                    )}
                  </span>
                  {showFailureButton && (
                    <FailureButton
                      size="small"
                      type="default"
                      onClick={() => handleShowFailureModal(item)}
                    >
                      {t("pttt.thatBai")}
                    </FailureButton>
                  )}
                </div>
              </FieldRenderContainer>
              {renderFailureInfo(item.id)}
            </div>
          );
        }

      case 20: // Chọn một - Single selection checkbox
        return (
          <div key={item.id}>
            <FieldRenderContainer
              $hasComment={hasComment}
              className={emptyClassName}
            >
              <FormItem
                name={fieldName}
                valuePropName="checked"
                $hasComment={hasComment}
              >
                <CustomRadio
                  onChange={(e) =>
                    handleSingleSelection(item, e.target.checked)
                  }
                  className="protocol-checkbox-wrapper"
                  softDisabled={disabled}
                >
                  {item.protocolTenTruong?.ten}
                </CustomRadio>
              </FormItem>
              {showFailureButton && (
                <FailureButton
                  size="small"
                  type="default"
                  onClick={() => handleShowFailureModal(item)}
                >
                  {t("pttt.thatBai")}
                </FailureButton>
              )}
              {hasComment && (
                <FormItem
                  label={""}
                  name={commentFieldName}
                  $hasComment={hasComment}
                  className="protocol-comment-item"
                >
                  <DeboundInput
                    type="multipleline"
                    readOnly={disabled}
                    markSpanRow={true}
                  />
                </FormItem>
              )}
            </FieldRenderContainer>
            {renderFailureInfo(item.id)}
          </div>
        );

      case 30: // Chọn nhiều - Multiple selection checkbox
        return (
          <div key={item.id}>
            <FieldRenderContainer
              $hasComment={hasComment}
              className={emptyClassName}
            >
              <FormItem
                name={fieldName}
                valuePropName="checked"
                $hasComment={hasComment}
              >
                <Checkbox
                  className="protocol-checkbox-wrapper"
                  softDisabled={disabled}
                >
                  {item.protocolTenTruong?.ten}
                </Checkbox>
              </FormItem>
              {showFailureButton && (
                <FailureButton
                  size="small"
                  type="default"
                  onClick={() => handleShowFailureModal(item)}
                >
                  {t("pttt.thatBai")}
                </FailureButton>
              )}
              {hasComment && (
                <FormItem
                  label={""}
                  name={commentFieldName}
                  $hasComment={hasComment}
                  className="protocol-comment-item"
                >
                  <DeboundInput
                    type="multipleline"
                    readOnly={disabled}
                    markSpanRow={true}
                  />
                </FormItem>
              )}
            </FieldRenderContainer>
            {renderFailureInfo(item.id)}
          </div>
        );

      default:
        return null;
    }
  };

  const renderFormItems = (items, parentItem = null) => {
    return items.map((item) => {
      let emptyClassName = "";

      if (hideEmptyFieldsOnPrint) {
        const isParentWithEmptyChildren =
          item.children &&
          item.children.length > 0 &&
          areAllChildrenEmpty(item);
        emptyClassName = isParentWithEmptyChildren
          ? "protocol-field-empty"
          : "";
      }

      return (
        <div key={item.id} className={emptyClassName}>
          {renderFormField(item, parentItem)}
          {item.children && item.children.length > 0 && (
            <ParentChildFormItem
              parentItem={item}
              formValues={formValues}
              t={t}
              isAfterFailedStep={isAfterFailedStep(item)}
            >
              <div style={{ marginLeft: "20px" }}>
                {renderFormItems(item.children, item)}
              </div>
            </ParentChildFormItem>
          )}
        </div>
      );
    });
  };

  if (!protocolId) {
    return null;
  }

  return (
    <>
      {hideEmptyFieldsOnPrint && <GlobalPrintStyle />}
      <div>
        {hierarchicalData.length > 0 ? (
          renderFormItems(hierarchicalData)
        ) : (
          <div style={{ textAlign: "center", padding: "20px" }}>
            {t("common.khongCoDuLieu")}
          </div>
        )}
      </div>
      <ModalDanhDauThatBai ref={refModalDanhDauThatBai} />
    </>
  );
};

export default ProtocolFormContent;
export { ParentChildFormItem };
