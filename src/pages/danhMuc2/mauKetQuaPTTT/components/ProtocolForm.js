import React, {
  useEffect,
  memo,
  forwardRef,
  useImperativeHandle,
  useCallback,
} from "react";
import { useQuery } from "@tanstack/react-query";
import { sortBy } from "lodash";
import { Form } from "antd";
import ProtocolFormContent from "./ProtocolFormContent";
import dmProtocolChiTietProvider from "data-access/categories/dm-protocol-chi-tiet-provider";
import { toSafePromise } from "lib-utils";

const getProtocolDataFromValues = (protocolId, values, listProtocolChiTiet) => {
  const dsProtocol = [];

  if (listProtocolChiTiet?.length > 0 && protocolId) {
    listProtocolChiTiet.forEach((item) => {
      const fieldName = `field_${item.id}`;
      const commentFieldName = `comment_${item.id}`;
      const thucHienThatBaiName = `thucHienThatBai_${item.id}`;
      const lyDoThatBaiName = `lyDoThatBai_${item.id}`;

      dsProtocol.push({
        protocolChiTietId: item.id,
        protocolChiTiet: item,
        noiDung: values[fieldName] || null,
        ghiChu: values[commentFieldName] || null,
        thucHienThatBai: values[thucHienThatBaiName] || false,
        lyDoThatBai: values[lyDoThatBaiName] || null,
      });
    });
  }

  return dsProtocol;
};

const ProtocolForm = (
  {
    protocolId,
    currentData,
    disabled = false,
    onValuesChange,
    onFinish,
    style,
    className,
    form: externalForm,
    hideEmptyFieldsOnPrint = false,
  },
  ref
) => {
  const [internalForm] = Form.useForm();
  const form = externalForm || internalForm;
  const formValues = Form.useWatch([], form);

  const { data: listProtocolChiTiet } = useQuery({
    queryKey: ["protocolChiTiet", "listData", protocolId],
    queryFn: () => {
      return dmProtocolChiTietProvider.search({
        protocolId: protocolId,
        page: 0,
        size: 2000,
        active: true,
      });
    },
    enabled: !!protocolId,
    select: useCallback(
      (res) =>
        sortBy(res.data, [
          (item) => item.stt ?? 999999,
          (item) => item.protocolTenTruong?.ma || "",
        ]),
      []
    ),
  });

  useImperativeHandle(ref, () => ({
    getFormValues: () => {
      const values = form.getFieldsValue();
      return values;
    },

    setFormValues: (values) => {
      form.setFieldsValue(values);
    },

    asyncSubmit: async () => {
      const [err, values] = await toSafePromise(form.validateFields());

      if (err) {
        return Promise.reject(err);
      }

      const protocolData = getProtocolDataFromValues(
        protocolId,
        values,
        listProtocolChiTiet
      );
      return Promise.resolve({ values, protocolData });
    },
  }));

  // Set initial values when data changes
  useEffect(() => {
    if (listProtocolChiTiet?.length > 0 && protocolId) {
      const initialValues = {};
      const initialComments = {};
      const initialFailureData = {};

      listProtocolChiTiet.forEach((item) => {
        const existingProtocolData = currentData?.find(
          (p) => p.protocolChiTietId === item.id
        );

        if (existingProtocolData) {
          initialValues[`field_${item.id}`] = existingProtocolData.noiDung;
          if (item.ghiChu) {
            initialComments[`comment_${item.id}`] =
              existingProtocolData.ghiChu || "";
          }

          if (item.thucHienThatBai) {
            initialFailureData[`thucHienThatBai_${item.id}`] =
              existingProtocolData.thucHienThatBai;
            initialFailureData[`lyDoThatBai_${item.id}`] =
              existingProtocolData.lyDoThatBai;
          }
        } else {
          if (item.loaiHienThi === 20 || item.loaiHienThi === 30) {
            if (item.macDinh) {
              initialValues[`field_${item.id}`] = true;
            }
          }

          if (item.ghiChu) {
            initialComments[`comment_${item.id}`] = "";
          }
        }
      });
      form.resetFields();
      form.setFieldsValue({
        ...initialValues,
        ...initialComments,
        ...initialFailureData,
      });
    } else {
      form.resetFields();
    }
  }, [listProtocolChiTiet, form, currentData, protocolId]);

  const handleSingleSelection = (newValues) => {
    form.setFieldsValue(newValues);
    if (onValuesChange) {
      const protocolData = getProtocolDataFromValues(
        protocolId,
        newValues,
        listProtocolChiTiet
      );
      onValuesChange(newValues, newValues, protocolData);
    }
  };

  const handleValuesChange = (changedValues, allValues) => {
    if (onValuesChange) {
      const protocolData = getProtocolDataFromValues(
        protocolId,
        allValues,
        listProtocolChiTiet
      );
      onValuesChange(changedValues, allValues, protocolData);
    }
  };

  const handleFinish = (values) => {
    if (onFinish) {
      const protocolData = getProtocolDataFromValues(
        protocolId,
        values,
        listProtocolChiTiet
      );

      onFinish(values, protocolData);
    }
  };

  const handleFailureUpdate = (protocolChiTietId, failureData) => {
    form.setFieldsValue({
      [`thucHienThatBai_${protocolChiTietId}`]: failureData.thucHienThatBai,
      [`lyDoThatBai_${protocolChiTietId}`]: failureData.lyDoThatBai,
    });
    form.validateFields(); // re-validate form
  };

  return (
    <Form
      form={form}
      onValuesChange={handleValuesChange}
      onFinish={handleFinish}
      style={style}
      className={className}
      layout="horizontal"
    >
      <ProtocolFormContent
        protocolId={protocolId}
        disabled={disabled}
        formValues={formValues}
        onSingleSelection={handleSingleSelection}
        listProtocolChiTiet={listProtocolChiTiet}
        hideEmptyFieldsOnPrint={hideEmptyFieldsOnPrint}
        onFailureUpdate={handleFailureUpdate}
      />
    </Form>
  );
};

export default memo(forwardRef(ProtocolForm));
