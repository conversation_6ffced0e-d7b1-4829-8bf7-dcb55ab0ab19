import { useState, useCallback, useEffect } from "react";
import { isObject } from "utils/index";

const useFormState = (otherInfo, form, refAddressFull) => {
  const [state, setState] = useState({
    ngaySinh: "",
    validate: 0,
    checkNgaySinh: false,
  });

  const updateState = useCallback((data) => {
    setState((prevState) => ({
      ...prevState,
      ...data,
    }));
  }, []);

  const onChangeNgaySinh = useCallback(
    (value) => {
      updateState({ ngaySinh: value });
      form.setFields([{ name: "ngaySinh", value }]);
    },
    [updateState, form]
  );

  const onChangeAddress = useCallback(
    (data) => {
      if (data) {
        form.setFieldsValue({
          soNha: data.soNha,
          xaPhuongId: data.xaPhuongId,
          quanHuyenId: data.quanHuyenId,
          tinhThanhPhoId: data.tinhThanhPhoId,
          quocGiaId: data.quocGiaId,
        });
      }
    },
    [form]
  );

  const onValidate = useCallback(
    (value) => {
      updateState({ validate: value });
    },
    [updateState]
  );

  useEffect(() => {
    if (isObject(otherInfo)) {
      const { ngaySinh, ...rest } = otherInfo;
      form.setFieldsValue(rest);
      updateState({ ngaySinh });

      if (rest.diaChi && refAddressFull.current) {
        refAddressFull.current.setAddress(rest.diaChi);
      }
    }
  }, [otherInfo, form, refAddressFull, updateState]);

  return {
    state,
    updateState,
    onChangeNgaySinh,
    onChangeAddress,
    onValidate,
  };
};

export default useFormState;
