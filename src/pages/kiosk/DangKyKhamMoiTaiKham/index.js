import React, {
  useState,
  useCallback,
  useRef,
  useEffect,
  useMemo,
} from "react";
import { useTranslation } from "react-i18next";
import { Form } from "antd";

import { useStore, useQueryAll } from "hooks";
import { getQueryStringValue } from "hooks/useQueryString/queryString";
import cacheUtils from "lib-utils/cache-utils";

import { isObject } from "utils/index";
import { SVG } from "assets";
import { Card } from "components";
import Button from "../common/Button";

import { KiosWrapper } from "../components";
import { MainWrapper, GlobalStyle } from "./styles";
import { LOAI_KHU_VUC } from "constants/index";

import { query } from "redux-store/stores";
import ModalChonKhuVuc from "../LuaChonHinhThucKham/ModalChonKhuVuc";
import FormDangKyKham from "./FormDangKyKham";
import { RESET_TIMEOUT, CACHE_KEYS } from "./constants";
import useQRCodeProcessor from "./hooks/useQRCodeProcessor";
import useRegistrationProcessor from "./hooks/useRegistrationProcessor";
import useKioskState from "./hooks/useKioskState";
import { BaseContent, SuccessContent, ErrorContent } from "./components";

const KioskDangKyKhamMoiTaiKham = React.memo(() => {
  const { t } = useTranslation();
  const [input, setInput] = useState("");
  const { state, actions } = useKioskState();
  const { parseQRCodeValue } = useQRCodeProcessor();
  const auth = useStore("auth.auth", {});
  const khuVucIdQuery = getQueryStringValue("khuVucId");
  const [form] = Form.useForm();

  const timeoutRef = useRef(null);
  const inputRef = useRef(null);
  const formRef = useRef(null);
  const refModalChonKhuVuc = useRef(null);

  const setState = actions.setState;

  const { data: listKhuVucTongHop, isLoading: loadingKhuVucTongHop } =
    useQueryAll(
      query.khuVuc.queryAllKhuVuc({
        params: {
          loai: LOAI_KHU_VUC.TIEP_DON,
          active: true,
        },
      })
    );

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!state.loading && !state.success && !state.error && inputRef.current) {
      inputRef.current.focus();
    }
  }, [state.loading, state.success, state.error]);

  const onOpenPopup = () => {
    refModalChonKhuVuc.current &&
      refModalChonKhuVuc.current.show(state.khuVuc, (data) => {
        cacheUtils.save(
          auth?.id,
          CACHE_KEYS.KHU_VUC_LAY_SO_TIEP_DON,
          data,
          false
        );
        actions.setKhuVuc(data);
      });
  };

  useEffect(() => {
    async function fetchData() {
      let khuVuc = await cacheUtils.read(
        auth.id,
        CACHE_KEYS.KHU_VUC_LAY_SO_TIEP_DON,
        null,
        false
      );
      if (!khuVuc) {
        onOpenPopup();
      } else {
        actions.setKhuVuc(khuVuc);
      }
    }

    if (!loadingKhuVucTongHop) {
      if (khuVucIdQuery) {
        const khuVuc = (listKhuVucTongHop || []).find(
          (i) => i.id === +khuVucIdQuery
        );
        if (khuVuc) {
          actions.setKhuVuc(khuVuc);
          cacheUtils.save(
            auth?.id,
            CACHE_KEYS.KHU_VUC_LAY_SO_TIEP_DON,
            khuVuc,
            false
          );
        } else if (auth?.id) {
          fetchData();
        }
      } else if (auth?.id) {
        fetchData();
      }
    }
  }, [listKhuVucTongHop, khuVucIdQuery, loadingKhuVucTongHop, auth]);

  const clearResetTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const scheduleReset = useCallback(() => {
    clearResetTimeout();
    timeoutRef.current = setTimeout(() => {
      actions.resetState();
    }, RESET_TIMEOUT);
  }, [clearResetTimeout, actions]);

  const { processRegistration, processNewRegistration } =
    useRegistrationProcessor(state.khuVuc, setState, scheduleReset);

  const parseQRCodeCallback = useCallback(
    (value) => {
      return parseQRCodeValue(value);
    },
    [parseQRCodeValue]
  );

  const onChange = useCallback((e) => {
    setInput(e.target.value.trim());
  }, []);

  const onKeyDown = useCallback(
    async (e) => {
      const value = e.target.value || input;

      if (e.key === "Enter" || e.key === "Tab") {
        if (!value || state.loading) return;

        const params = parseQRCodeCallback(value);
        if (params && isObject(params, true)) {
          setInput("");
          await processRegistration(params);
        }
      }
    },
    [input, state.loading, parseQRCodeCallback, processRegistration]
  );

  const onClickDangKy = async () => {
    if (input) {
      const params = parseQRCodeCallback(input);
      if (params && isObject(params, true)) {
        setInput("");
        await processRegistration(params);
      }
    } else {
      actions.setShowForm(true);
    }
  };

  const onSubmitForm = () => {
    formRef.current?.submit();
  };

  const onFinsishForm = async (values) => {
    const formData = {
      ...values,
      ngayCapGiayToTuyThan:
        values.ngayCapGiayToTuyThan || state.otherInfo?.ngayCapGiayToTuyThan,
    };
    await processNewRegistration(formData);
  };

  const onBack = () => {
    actions.resetState();
  };

  const renderBaseContent = () => {
    return (
      <>
        <BaseContent
          input={input}
          loading={state.loading}
          error={state.error}
          onKeyDown={onKeyDown}
          onChange={onChange}
          inputRef={inputRef}
        />
        <div className="btn-action">
          <Button
            onClick={onClickDangKy}
            rounded
            className="btn-md"
            bxShadow="#05c270"
            padding={60}
            disabled={state.loading}
          >
            <SVG.IcKhamThuong />
            <span>{t("kiosk.dangKy")}</span>
          </Button>
        </div>
      </>
    );
  };

  const renderTitle = useMemo(() => {
    if (state.showForm) {
      return t("kiosk.dangKyKhamBenh");
    } else if (state.success) {
      return t("kiosk.laySo");
    } else {
      return t("kiosk.laySoThuTuTiepDon").toUpperCase();
    }
  }, [state.showForm, state.success]);

  return (
    <KiosWrapper onLogoClick={onOpenPopup}>
      <GlobalStyle />
      <MainWrapper>
        {!state.success && !state.error && !state.showForm && (
          <header className="top">
            <div className="header">
              <h1 className="title">{t("kiosk.kioskTiepDonNguoiBenh")}</h1>
              <p className="sub-header">{t("kiosk.xinKinhChaoQuyKhach")}</p>
            </div>
          </header>
        )}
        <main className={"content"} role="main">
          <div className="header-content" role="banner">
            {state.showForm ? (
              <SVG.IcEdit aria-hidden="true" />
            ) : (
              <SVG.IcLaySo aria-hidden="true" />
            )}
            <span>{renderTitle}</span>
          </div>
          <Card
            className="card-content"
            role="region"
            aria-labelledby={
              state.success
                ? "success-title"
                : state.error
                ? "error-title"
                : "main-title"
            }
          >
            {state.success ? (
              <SuccessContent stt={state.stt} />
            ) : state.error ? (
              <ErrorContent error={state.error} />
            ) : state.showForm ? (
              <>
                <FormDangKyKham
                  otherInfo={state.otherInfo}
                  form={form}
                  ref={formRef}
                  onFinish={onFinsishForm}
                />
                <div className="btn-action">
                  <div className="btn-group">
                    <Button
                      onClick={onBack}
                      className="btn-md btn-save"
                      rounded
                      padding={60}
                      bxShadow="#9f9fa1"
                    >
                      <SVG.IcArrowLeft />
                      <span>{t("kiosk.quayLai")}</span>
                    </Button>
                    <Button
                      htmlType="submit"
                      className="btn-md btn-save"
                      rounded
                      padding={60}
                      bxShadow="#05c270"
                      onClick={onSubmitForm}
                      disabled={state.loading}
                    >
                      <SVG.IcSave />
                      <span>{t("kiosk.luuVaLaySo")}</span>
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              renderBaseContent()
            )}
          </Card>
        </main>
        <ModalChonKhuVuc ref={refModalChonKhuVuc} />
      </MainWrapper>
    </KiosWrapper>
  );
});

KioskDangKyKhamMoiTaiKham.displayName = "KioskDangKyKhamMoiTaiKham";

export default KioskDangKyKhamMoiTaiKham;
