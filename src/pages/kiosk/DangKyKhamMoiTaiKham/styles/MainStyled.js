import styled, { keyframes } from "styled-components";
import {
  titleColor,
  textColor,
  subtitleColor,
  customGray,
  customBlue2,
  btnGreen,
} from "../../common/variables";

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

const slideInFromBottom = keyframes`
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const breakpoints = {
  mobile: "768px",
  tablet: "1024px",
  desktop: "1440px",
  kiosk: "1920px",
  kioskPortrait: "1080px",
  kioskLandscape: "1920px",
};

const kioskFontSizes = {
  h1: "clamp(24px, 2.5vw, 32px)",
  h2: "clamp(20px, 2vw, 28px)",
  body: "clamp(16px, 1.5vw, 20px)",
  small: "clamp(12px, 1vw, 16px)",
};

export const MainWrapper = styled.div`
  overflow: auto;
  text-align: center;
  height: 100%;
  animation: ${fadeIn} 0.5s ease-out;

  @media (orientation: portrait) {
    padding: clamp(5px, 1vh, 10px);
  }

  @media (orientation: landscape) {
    padding: clamp(8px, 1.5vh, 15px);
  }

  .form-custom {
    display: flex;
    flex-direction: column;
    height: 100%;

    .ant-form-item {
      margin-bottom: 0;
      .ant-form-item-label {
        padding-bottom: clamp(8px, 1.2vw, 12px) !important;

        label {
          font-size: clamp(16px, 2.5vw, 22px) !important;
          font-weight: 700;
          color: #2c3e50;
          line-height: 1.4;
          text-transform: uppercase;
          letter-spacing: 0.3px;

          &::after {
            display: none; // Ẩn dấu : mặc định
          }
        }
      }

      .ant-input-affix-wrapper {
        border-radius: clamp(25px, 4vw, 32px);
        box-shadow: 0 0 0 3px #0062ff47 !important;
        overflow: hidden;
      }
      .address-full-wrapper {
        border-radius: clamp(25px, 4vw, 32px);
        box-shadow: 0 0 0 3px #0062ff47 !important;
        padding: 4px 11px;
        ul {
          position: absolute;
          width: 100%;
          background: #ffffff;
          border-radius: 16px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15),
            0 2px 8px rgba(0, 0, 0, 0.08);
          z-index: 999;
          max-height: 280px;
          overflow-y: auto;
          border: 2px solid #e8f0fe;
          margin-top: 8px;
          left: 0;
          padding: 8px 0;
          list-style: none;

          ::-webkit-scrollbar {
            width: 8px;
          }
          ::-webkit-scrollbar-track {
            background: #f8fafc;
            border-radius: 4px;
          }
          ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #0762f7, #4285f4);
            border-radius: 4px;
            border: 1px solid #e8f0fe;
          }
          ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #0651d3, #3367d6);
          }

          li {
            font-size: clamp(16px, 2.5vw, 22px);
            text-align: left;
            padding: 12px 16px;
            margin: 2px 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #2c3e50;
            font-weight: 500;
            line-height: 1.4;
            border: 1px solid transparent;

            &:hover {
              background: linear-gradient(135deg, #f0f6ff 0%, #e8f4fd 100%);
              border-color: #0762f7;
              color: #0762f7;
              transform: translateX(4px);
              box-shadow: 0 2px 8px rgba(7, 98, 247, 0.15);
            }

            &:active {
              transform: translateX(2px);
              background: linear-gradient(135deg, #e8f4fd 0%, #dbeafe 100%);
            }
          }

          li:last-child {
            margin-bottom: 0;
          }
        }
      }

      .ant-input,
      .ant-picker,
      .address-full-wrapper input,
      .item-dob input {
        height: clamp(50px, 7vw, 65px) !important;
        font-size: ${kioskFontSizes.body};
        padding: clamp(12px, 2vw, 18px) clamp(18px, 3vw, 24px) !important;
        transition: all 0.3s ease;
        background: #ffffff;
        border: none !important;
        box-shadow: none !important;
        font-weight: 500;

        &::placeholder {
          color: #8b9bb8;
          opacity: 0.9;
          font-weight: 400;
        }
      }
      .address-full-wrapper input {
        width: 100%;
      }
      .ant-input-suffix {
        span {
          font-size: ${kioskFontSizes.body};
        }
      }

      .ant-picker {
        width: 100%;

        .ant-picker-input input {
          font-size: ${kioskFontSizes.body};
          font-weight: 500;
        }

        .ant-picker-suffix {
          font-size: ${kioskFontSizes.body};
          color: #0762f7;
        }
      }

      /* Đảm bảo DOBInput có kích thước phù hợp */
      .item-dob {
        .ant-picker {
          height: clamp(50px, 7vw, 65px) !important;
          border-radius: clamp(25px, 4vw, 32px) !important;
          border: 3px solid #0062ff47 !important;

          .ant-picker-input input {
            font-size: ${kioskFontSizes.body} !important;
            padding: clamp(12px, 2vw, 18px) clamp(18px, 3vw, 24px) !important;
          }

          .ant-picker-suffix {
            font-size: ${kioskFontSizes.body} !important;
            margin-right: clamp(12px, 2vw, 18px);
          }
        }

        .ant-picker-dropdown {
          .ant-picker-panel-container {
            font-size: ${kioskFontSizes.body};
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
          }

          .ant-picker-header {
            button {
              font-size: ${kioskFontSizes.body};
            }
          }

          .ant-picker-content {
            th,
            td {
              font-size: ${kioskFontSizes.small};
              height: 32px;
              line-height: 32px;
            }
          }
        }
      }

      .ant-radio-group {
        display: flex;
        gap: clamp(25px, 4vw, 35px);
        margin-top: clamp(8px, 1.5vw, 12px);

        .ant-radio-wrapper {
          font-size: clamp(16px, 2.5vw, 22px);
          font-weight: 600;
          color: #2c3e50;
          display: flex;
          align-items: center;
          cursor: pointer;

          .ant-radio {
            .ant-radio-inner {
              width: clamp(20px, 3vw, 26px);
              height: clamp(20px, 3vw, 26px);
              border-color: #0762f7;

              &::after {
                width: clamp(20px, 3vw, 26px);
                height: clamp(20px, 3vw, 26px);
                background-color: #0762f7;
                margin-top: 0;
                margin-left: 0;
                transform: translateX(-50%) translateY(-50%) scale(0.5);
              }
            }
          }
        }
      }
    }

    .helper-text {
      font-size: ${kioskFontSizes.small};
      color: #7a869a;
      font-style: italic;
      line-height: 1.3;
      text-align: left;
      margin-left: clamp(10px, 2vw, 15px);
      margin-top: clamp(5px, 1vw, 10px);
      &.error-msg {
        color: #ff4d4f;
      }
    }

    .btn-save {
      font-size: ${kioskFontSizes.body};
      font-weight: 700;

      svg {
        width: clamp(20px, 2.5vw, 28px);
        height: clamp(20px, 2.5vw, 28px);
        color: #ffffff;
      }

      span {
        font-size: ${kioskFontSizes.body};
        font-weight: 700;
      }
    }
  }

  .top {
    overflow: hidden;
    padding-bottom: clamp(14px, 2vw, 20px);

    .header {
      display: flex;
      flex-direction: column;
      padding: clamp(10px, 2vw, 15px);
      gap: clamp(10px, 2vw, 15px);

      .title {
        font-weight: 800;
        font-size: ${kioskFontSizes.h1};
        line-height: 1.2;
        text-align: center;
        letter-spacing: 0.01em;
        text-transform: uppercase;
        color: ${titleColor};
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 0;
      }

      .sub-header {
        color: ${subtitleColor};
        font-weight: 400;
        font-size: ${kioskFontSizes.body};
        margin: 0;
        opacity: 0.9;
      }
    }

    .desc {
      margin: clamp(20px, 4vw, 40px) clamp(15px, 3vw, 30px) 0;
      font-weight: bold;
      font-size: ${kioskFontSizes.h2};
      line-height: 1.3;
      text-align: center;
      letter-spacing: 0.01em;
      color: ${textColor};
    }
  }

  .content {
    width: clamp(350px, 90vw, 900px);
    max-width: 95vw;
    flex: 1;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    margin-bottom: clamp(8px, 1.5vw, 15px);
    border-top-left-radius: clamp(12px, 2.5vw, 24px);
    border-top-right-radius: clamp(12px, 2.5vw, 24px);
    background: linear-gradient(135deg, #f0f6ff 0%, #e8f4fd 100%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    animation: ${slideInFromBottom} 0.6s ease-out;

    &.no-header {
      margin-top: clamp(20px, 4vw, 50px);
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: clamp(8px, 1.5vw, 16px);
      padding: clamp(12px, 2vw, 20px) 0;
      font-size: ${kioskFontSizes.h2};
      font-weight: 700;
      color: ${titleColor};

      svg {
        width: clamp(24px, 3vw, 40px) !important;
        height: clamp(24px, 3vw, 40px) !important;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
      }
    }

    .card-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: clamp(10px, 1.8vw, 20px);

      h1 {
        font-size: ${kioskFontSizes.h1};
        font-weight: 700;
        margin-bottom: clamp(15px, 2vw, 25px);
        color: ${titleColor};
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      p {
        font-size: ${kioskFontSizes.body};
        color: ${subtitleColor};
        line-height: 1.5;
        margin-bottom: clamp(25px, 4vw, 40px);
        opacity: 0.9;
      }

      .icon-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;

        gap: clamp(10px, 1.8vw, 20px);
        svg {
          height: clamp(80px, 12vh, 160px);
          width: auto;
          max-width: 25%;
          object-fit: contain;
        }
      }

      .input-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: clamp(15px, 2vw, 25px);
        margin-top: clamp(10px, 2vw, 20px);

        .input-search {
          background: #ffffff;
          border: 3px solid ${btnGreen};
          box-sizing: border-box;
          border-radius: clamp(25px, 4vw, 50px);
          display: flex;
          min-width: 300px;
          width: 100%;
          max-width: 600px;
          align-items: center;
          justify-content: center;
          padding: clamp(12px, 2vw, 20px) clamp(16px, 2.5vw, 24px);
          box-shadow: 0 4px 16px rgba(4, 146, 84, 0.2);
          transition: all 0.3s ease;
          position: relative;

          &:hover {
            box-shadow: 0 6px 24px rgba(4, 146, 84, 0.3);
            transform: translateY(-2px);
          }

          &:focus-within {
            box-shadow: 0 0 0 4px rgba(4, 146, 84, 0.2);
            border-color: ${btnGreen};
          }

          .ant-input-affix-wrapper {
            border: none;
            background: transparent;
            font-size: ${kioskFontSizes.body};

            &:focus,
            &-focused {
              border: none;
              box-shadow: none;
            }
          }

          input {
            border: none;
            font-weight: 600;
            font-size: ${kioskFontSizes.body} !important;
            line-height: 1.4;
            color: ${textColor};

            &:hover,
            &:focus {
              border: none !important;
              box-shadow: none !important;
            }

            &::placeholder {
              color: #7a869a;
              opacity: 0.7;
            }

            &:disabled {
              background: transparent;
              color: ${textColor};
              opacity: 0.7;
            }
          }

          .ant-input-suffix {
            svg {
              width: clamp(20px, 2.5vw, 32px);
              height: clamp(20px, 2.5vw, 32px);
              color: ${btnGreen};
            }
          }
        }

        .loading-text {
          font-size: ${kioskFontSizes.body};
          color: ${btnGreen};
          font-weight: 600;
          animation: ${pulse} 1.5s ease-in-out infinite;
          text-align: center;
        }
      }

      .btn-action {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
        padding-bottom: clamp(10px, 1.8vw, 20px);
        font-size: ${kioskFontSizes.body};
        .btn-group {
          display: flex;
          justify-content: space-around;
          align-items: center;
          width: 100%;
        }
      }

      .success-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: clamp(5px, 1vw, 12px);
        width: 100%;
        height: 100%;
        animation: ${fadeIn} 0.8s ease-out;

        svg {
          width: clamp(40px, 5vw, 70px) !important;
          height: clamp(40px, 5vw, 70px) !important;
          filter: drop-shadow(0 4px 8px rgba(4, 146, 84, 0.3));
          animation: ${pulse} 2s ease-in-out infinite;
        }

        h1 {
          font-size: ${kioskFontSizes.h2};
          font-weight: 700;
          color: ${btnGreen};
          text-align: center;
          line-height: 1.2;
          text-shadow: 0 2px 4px rgba(4, 146, 84, 0.2);
          margin: 0 clamp(15px, 2vw, 25px);
        }
        p {
          font-size: ${kioskFontSizes.h2};
          color: ${subtitleColor};
        }

        .stt-content {
          margin: clamp(8px, 1.2vw, 15px) auto;
          background: linear-gradient(135deg, ${customGray} 0%, #f5f8fa 100%);
          border: clamp(2px, 0.2vw, 3px) dashed ${customBlue2};
          box-sizing: border-box;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
          border-radius: clamp(16px, 2.5vw, 32px);
          width: clamp(280px, 45vw, 420px);
          padding: clamp(15px, 2vw, 25px) clamp(12px, 1.5vw, 20px);
          text-align: center;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: linear-gradient(45deg, ${customBlue2}, ${btnGreen});
            border-radius: inherit;
            z-index: -1;
            opacity: 0.08;
          }

          .stt {
            font-size: ${kioskFontSizes.body};
            line-height: 1.3;
            color: ${textColor};
            margin-bottom: clamp(8px, 1vw, 12px);
            font-weight: 600;
          }

          .number {
            font-size: clamp(36px, 8vw, 80px);
            line-height: 0.9;
            color: ${customBlue2};
            font-weight: 900;
            text-shadow: -1px -1px 2px rgba(255, 255, 255, 0.8),
              1px 1px 4px rgba(0, 0, 0, 0.15);
            margin: clamp(5px, 1vw, 10px) 0;
            animation: ${pulse} 3s ease-in-out infinite;
          }

          .sub-txt {
            color: #e53935;
            font-size: ${kioskFontSizes.small};
          }

          .guideline {
            color: ${textColor};
            padding: clamp(8px, 1.2vw, 15px);
            font-size: ${kioskFontSizes.small};
            line-height: 1.3;
            opacity: 0.8;
          }
        }

        .footer {
          margin-top: clamp(8px, 1vw, 15px);

          .footer-text {
            text-align: center;
            color: ${titleColor};
            font-size: ${kioskFontSizes.small};
            font-weight: 600;
            margin-bottom: clamp(5px, 0.8vw, 10px);
          }

          .image {
            text-align: center;

            img {
              height: clamp(24px, 2.5vw, 35px);
              width: clamp(24px, 2.5vw, 35px);
              filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
              animation: ${pulse} 2s ease-in-out infinite;
            }
          }
        }
      }

      .error-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: clamp(15px, 2.5vw, 30px);
        width: 100%;
        height: 100%;
        animation: ${fadeIn} 0.8s ease-out;
        padding: clamp(20px, 3vw, 40px);

        svg {
          width: clamp(60px, 8vw, 120px) !important;
          height: clamp(60px, 8vw, 120px) !important;
          filter: drop-shadow(0 4px 8px rgba(229, 57, 53, 0.3));
        }

        h1 {
          font-size: ${kioskFontSizes.h2};
          color: #e53935;
          text-align: center;
          line-height: 1.4;
          font-weight: 600;
          margin: 0 clamp(20px, 3vw, 40px);
        }
        span {
          color: ${textColor};
        }
      }
    }
  }

  @media (max-width: 1200px) {
    .content {
      width: 98vw;
      margin-bottom: 5px;
      border-radius: 8px;

      &.no-header {
        margin-top: clamp(10px, 2vw, 25px);
      }

      .card-content {
        padding: 8px 12px;

        h1 {
          font-size: clamp(18px, 4vw, 28px);
          margin-bottom: 8px;
        }

        p {
          font-size: clamp(12px, 2.5vw, 16px);
          margin-bottom: 8px;
        }
      }
    }

    /* Form responsive styles for tablets */
    .form-dang-ky-kham {
      padding: clamp(15px, 2.5vw, 25px);
      max-width: 95vw;

      .form-header {
        margin-bottom: clamp(15px, 2vw, 20px);
        padding-bottom: clamp(10px, 1.5vw, 15px);

        span {
          font-size: clamp(16px, 3vw, 24px);
        }
      }

      .form-custom {
        .ant-form-item {
          .ant-input,
          .ant-picker {
            height: clamp(40px, 5vw, 50px);
            font-size: clamp(14px, 2.5vw, 18px);
            padding: clamp(6px, 1.2vw, 12px) clamp(12px, 2vw, 16px);
            .ant-picker-suffix {
              font-size: clamp(14px, 2.5vw, 18px);
            }
          }

          /* Responsive styles cho DOBInput */
          .item-dob {
            .ant-picker {
              height: clamp(40px, 5vw, 50px) !important;
              border-radius: clamp(20px, 3vw, 25px) !important;

              .ant-picker-input input {
                font-size: clamp(14px, 2.5vw, 18px) !important;
                padding: clamp(6px, 1.2vw, 12px) clamp(12px, 2vw, 16px) !important;
              }

              .ant-picker-suffix {
                font-size: clamp(14px, 2.5vw, 18px) !important;
                margin-right: clamp(8px, 1.5vw, 12px);
              }
            }
          }

          .ant-form-item-explain-error {
            font-size: clamp(10px, 1.8vw, 14px);
            margin-left: clamp(5px, 1vw, 10px);
            text-align: left;
          }

          .ant-radio-group {
            gap: clamp(20px, 3vw, 25px);

            .ant-radio-wrapper {
              font-size: clamp(14px, 2.5vw, 18px);

              .ant-radio .ant-radio-inner {
                width: clamp(16px, 2vw, 20px);
                height: clamp(16px, 2vw, 20px);

                &::after {
                  width: clamp(16px, 2vw, 20px);
                  height: clamp(16px, 2vw, 20px);
                }
              }
            }
          }
        }

        .helper-text {
          font-size: clamp(10px, 1.8vw, 14px);
          margin-left: clamp(5px, 1vw, 10px);
          margin-top: clamp(5px, 1vw, 10px);
        }

        .btn-save {
          min-width: clamp(180px, 25vw, 250px);
          height: clamp(45px, 6vw, 60px);
          font-size: clamp(14px, 2.5vw, 18px);

          svg {
            width: clamp(18px, 2vw, 24px);
            height: clamp(18px, 2vw, 24px);
          }
        }
      }
    }

    .content {
      .card-content {
        .success-content {
          gap: clamp(3px, 0.8vw, 8px);

          .stt-content {
            margin: clamp(5px, 0.8vw, 10px) auto;
            padding: clamp(8px, 1.5vw, 15px);

            .number {
              font-size: clamp(24px, 6vw, 48px);
            }

            .stt {
              font-size: clamp(10px, 2vw, 14px);
            }
          }
        }

        .footer {
          margin-top: clamp(5px, 0.8vw, 10px);

          .footer-text {
            font-size: clamp(10px, 2vw, 14px);
            margin-bottom: clamp(3px, 0.5vw, 6px);
          }

          .image img {
            height: clamp(18px, 2vw, 28px);
            width: clamp(18px, 2vw, 28px);
          }
        }
      }
    }
  }

  @media (max-width: ${breakpoints.tablet}) {
    .content {
      width: 95vw;
      margin-bottom: 10px;

      .card-content {
        padding: 20px 15px;

        .input-wrapper .input-search {
          min-width: 250px;
          padding: 15px 20px;
        }
      }
    }
  }

  @media (min-width: ${breakpoints.kiosk}) {
    .content {
      max-width: 1000px;

      .card-content {
        padding: 50px;

        .icon-wrapper {
          svg {
            height: clamp(120px, 15vh, 200px);
          }
        }

        .input-wrapper .input-search {
          max-width: 700px;
          padding: 25px 30px;
        }

        .success-content .stt-content {
          width: 600px;
          padding: 50px 40px;
        }
      }
    }
  }

  @media (max-width: 800px) {
    .content {
      &.no-header {
        margin-top: 5px;
      }
    }
  }

  @media (max-width: ${breakpoints.kioskPortrait}) and (orientation: portrait) {
    .top .header {
      .title {
        font-size: clamp(24px, 3vh, 42px);
      }
      .sub-header {
        font-size: clamp(18px, 2.5vh, 38px);
      }
    }
    .content {
      width: 95vw;
      max-width: 1000px;
      max-height: 90vh;
      margin: 0 auto;
      border-radius: 12px;
      overflow-y: auto;

      &.no-header {
        margin-top: clamp(5px, 1vh, 10px);
        max-height: 95vh;
      }

      .header-content {
        font-size: clamp(20px, 2.5vh, 36px);
      }

      .card-content {
        padding: clamp(10px, 2vh, 20px) clamp(12px, 2vw, 20px);

        h1 {
          font-size: clamp(28px, 6vh, 80px);
          margin-bottom: clamp(8px, 1.5vh, 15px);
        }

        p {
          font-size: clamp(20px, 4.5vh, 60px);
          margin-bottom: clamp(10px, 2vh, 20px);
        }

        .icon-wrapper {
          justify-content: space-around;
          svg {
            height: auto;
            width: auto;
          }
        }

        .input-wrapper .input-search {
          min-width: 280px !important;
          max-width: 90vw !important;
          padding: clamp(12px, 2.5vh, 18px) clamp(15px, 3vw, 20px) !important;
          font-size: clamp(18px, 2.5vh, 38px) !important;
          margin-top: clamp(12px, 2.5vh, 18px);

          .ant-input-affix-wrapper {
            font-size: clamp(18px, 2.5vh, 38px) !important;
            input {
              font-size: clamp(18px, 2.5vh, 38px) !important;
            }
          }
          .ant-input-suffix {
            svg {
              width: clamp(20px, 2.5vh, 38px);
              height: clamp(20px, 2.5vh, 38px);
              color: ${btnGreen};
            }
          }
        }
        .loading-text {
          font-size: clamp(18px, 2.5vh, 38px) !important;
        }
        .item-dob input,
        .address-full-wrapper input {
          font-size: clamp(18px, 2.5vh, 38px) !important;
        }
        .address-full-wrapper {
          ul {
            border-radius: 12px;
            box-shadow: 0 6px 24px rgba(0, 0, 0, 0.18),
              0 2px 8px rgba(0, 0, 0, 0.1);
            max-height: 320px;
            padding: 6px 0;

            li {
              font-size: clamp(18px, 2.5vh, 38px) !important;
              padding: 14px 18px;
              margin: 2px 6px;

              &:hover {
                transform: translateX(6px);
                box-shadow: 0 3px 12px rgba(7, 98, 247, 0.2);
              }
            }
          }
        }

        .btn-action {
          font-size: clamp(18px, 2.5vh, 38px) !important;
        }

        .success-content {
          gap: clamp(8px, 1.5vh, 15px);

          svg {
            width: clamp(50px, 10vh, 120px) !important;
            height: clamp(50px, 10vh, 120px) !important;
          }

          h1 {
            font-size: clamp(28px, 6vh, 80px);
          }

          p {
            font-size: clamp(20px, 4.5vh, 60px);
          }

          .stt-content {
            width: 90%;
            max-width: 800px;
            margin: clamp(8px, 1.5vh, 15px) auto;
            padding: clamp(15px, 3vh, 25px);

            .number {
              font-size: clamp(40px, 10vh, 180px);
            }

            .stt {
              font-size: clamp(20px, 3.5vh, 44px);
            }
          }

          .footer {
            margin-top: clamp(8px, 1.5vh, 15px);

            .footer-text {
              font-size: clamp(18px, 3vh, 38px);
            }

            .image img {
              height: clamp(20px, 3vh, 60px);
              width: clamp(20px, 3vh, 60px);
            }
          }
        }
        .error-content {
          svg {
            width: clamp(60px, 8vh, 120px) !important;
            height: clamp(60px, 8vh, 120px) !important;
          }
          h1 {
            font-size: clamp(28px, 6vh, 80px);
          }
        }
      }
    }

    /* Form styles for kiosk portrait */
    .form-header {
      margin-bottom: clamp(20px, 3vh, 30px);
      padding-bottom: clamp(15px, 2.5vh, 25px);

      svg {
        width: clamp(32px, 5vh, 45px);
        height: clamp(32px, 5vh, 45px);
      }

      span {
        font-size: clamp(28px, 5vh, 42px);
      }
    }

    .form-custom {
      .ant-form-item {
        .ant-form-item-label label {
          font-size: clamp(24px, 4vh, 36px) !important;
          &.ant-form-item-required:not(
              .ant-form-item-required-mark-optional
            )::before {
            font-size: clamp(24px, 4vh, 36px) !important;
            margin-right: clamp(10px, 1.5vh, 20px);
          }
        }

        .ant-form-item-explain-error {
          font-size: clamp(18px, 3vh, 28px);
          margin-left: clamp(10px, 1.5vh, 20px);
          text-align: left;
        }

        .ant-input,
        .ant-picker {
          height: clamp(60px, 8vh, 80px);
          font-size: clamp(24px, 4vh, 36px);
          padding: clamp(15px, 2.5vh, 22px) clamp(20px, 3.5vw, 30px);
          border-radius: clamp(30px, 5vh, 40px);
          border: 3px solid #dce3e9;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
          .ant-picker-suffix {
            font-size: clamp(24px, 4vh, 36px);
          }
        }

        /* Kiosk portrait styles cho DOBInput */
        .item-dob {
          .ant-picker {
            height: clamp(60px, 8vh, 80px) !important;
            border-radius: clamp(30px, 5vh, 40px) !important;
            border: 3px solid #0062ff47 !important;

            .ant-picker-input input {
              font-size: clamp(24px, 4vh, 36px) !important;
              padding: clamp(15px, 2.5vh, 22px) clamp(20px, 3.5vw, 30px) !important;
            }

            .ant-picker-suffix {
              font-size: clamp(24px, 4vh, 36px) !important;
              margin-right: clamp(15px, 2.5vh, 22px);
            }
          }

          .ant-picker-dropdown {
            .ant-picker-panel-container {
              font-size: clamp(20px, 3.5vh, 32px);
              border-radius: 16px;
            }

            .ant-picker-header {
              button {
                font-size: clamp(20px, 3.5vh, 32px);
              }
            }

            .ant-picker-content {
              th,
              td {
                font-size: clamp(18px, 3vh, 28px);
                height: clamp(40px, 6vh, 50px);
                line-height: clamp(40px, 6vh, 50px);
              }
            }
          }
        }
        .ant-input-suffix {
          span {
            font-size: clamp(24px, 4vh, 36px);
          }
        }

        .ant-radio-group {
          gap: clamp(30px, 5vw, 40px);

          .ant-radio-wrapper {
            font-size: clamp(24px, 4vh, 36px);

            .ant-radio .ant-radio-inner {
              width: clamp(24px, 4vh, 32px);
              height: clamp(24px, 4vh, 32px);

              &::after {
                width: clamp(24px, 4vh, 32px);
                height: clamp(24px, 4vh, 32px);
              }
            }
          }
        }
      }

      .helper-text {
        font-size: clamp(18px, 3vh, 28px);
        margin-left: clamp(10px, 1.5vw, 20px);
        margin-top: clamp(5px, 1vh, 10px);
      }

      .btn-save {
        min-width: clamp(280px, 40vw, 450px);
        height: clamp(70px, 9vh, 90px);
        font-size: clamp(24px, 4vh, 36px);
        border-radius: clamp(35px, 6vh, 45px);

        svg {
          width: clamp(28px, 4vh, 40px);
          height: clamp(28px, 4vh, 40px);
        }

        span {
          font-size: clamp(24px, 4vh, 36px);
        }
      }
    }
  }

  @media (min-width: ${breakpoints.kioskPortrait}) and (orientation: landscape) {
    .content {
      width: 85vw;
      max-width: 1200px;
      margin: 0 auto;

      .card-content {
        padding: clamp(25px, 4vh, 40px) clamp(20px, 3vw, 35px);

        h1 {
          font-size: clamp(28px, 4vh, 45px);
          margin-bottom: clamp(18px, 3vh, 30px);
        }

        p {
          font-size: clamp(18px, 2.5vh, 26px);
          margin-bottom: clamp(20px, 4vh, 35px);
        }

        .input-wrapper .input-search {
          min-width: 400px;
          max-width: 800px;
          padding: clamp(18px, 3vh, 25px) clamp(25px, 4vw, 35px);
          font-size: clamp(20px, 3vh, 28px);
        }

        .success-content {
          gap: clamp(12px, 2vh, 20px);

          svg {
            width: clamp(50px, 7vh, 80px) !important;
            height: clamp(50px, 7vh, 80px) !important;
          }

          h1 {
            font-size: clamp(24px, 4vh, 38px);
          }

          p {
            font-size: clamp(16px, 2.5vh, 22px);
          }

          .stt-content {
            width: clamp(400px, 50vw, 700px);
            margin: clamp(15px, 2vh, 25px) auto;
            padding: clamp(25px, 4vh, 40px);

            .number {
              font-size: clamp(40px, 10vh, 70px);
            }

            .stt {
              font-size: clamp(14px, 2.2vh, 18px);
            }
          }

          .footer {
            margin-top: clamp(12px, 2vh, 20px);

            .footer-text {
              font-size: clamp(13px, 2vh, 16px);
            }

            .image img {
              height: clamp(20px, 3vh, 30px);
              width: clamp(20px, 3vh, 30px);
            }
          }
        }
      }
    }
  }

  @media (max-height: 600px) {
    .content {
      .card-content {
        padding: 8px 12px;

        .success-content {
          gap: 5px;

          .stt-content {
            padding: 10px;
            margin: 5px auto;
          }

          .footer {
            margin-top: 5px;
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .content {
      width: 98vw;
      margin: 2px auto;
      border-radius: 8px;

      .card-content {
        padding: 8px;

        .input-wrapper .input-search {
          min-width: 200px;
          padding: 10px 15px;
        }
      }
    }

    /* Form mobile styles */
    .form-dang-ky-kham {
      padding: 12px;
      max-width: 98vw;
      border-radius: 12px;

      .form-header {
        margin-bottom: 15px;
        padding-bottom: 10px;

        svg {
          width: 20px;
          height: 20px;
        }

        span {
          font-size: 16px;
        }
      }

      .form-custom {
        .ant-form-item {
          .ant-form-item-label label {
            font-size: 14px !important;
          }

          .ant-form-item-explain-error {
            font-size: 11px;
            margin-left: 5px;
            text-align: left;
          }

          .ant-input,
          .ant-picker {
            height: 40px;
            font-size: 14px;
            padding: 8px 12px;
            border-radius: 20px;
          }

          /* Mobile styles cho DOBInput */
          .item-dob {
            .ant-picker {
              height: 40px !important;
              border-radius: 20px !important;
              border: 2px solid #0062ff47 !important;

              .ant-picker-input input {
                font-size: 14px !important;
                padding: 8px 12px !important;
              }

              .ant-picker-suffix {
                font-size: 14px !important;
                margin-right: 8px;
              }
            }

            .ant-picker-dropdown {
              .ant-picker-panel-container {
                font-size: 12px;
                border-radius: 8px;
              }

              .ant-picker-header {
                button {
                  font-size: 12px;
                }
              }

              .ant-picker-content {
                th,
                td {
                  font-size: 12px;
                  height: 24px;
                  line-height: 24px;
                }
              }
            }
          }
          .ant-input-suffix {
            span {
              font-size: 14px;
            }
          }

          .ant-radio-group {
            gap: 15px;

            .ant-radio-wrapper {
              font-size: 14px;

              .ant-radio .ant-radio-inner {
                width: 16px;
                height: 16px;

                &::after {
                  width: 16px;
                  height: 16px;
                }
              }
            }
          }
        }

        .helper-text {
          font-size: 11px;
          margin-left: 5px;
          margin-top: 2px;
        }

        .btn-save {
          min-width: 160px;
          height: 45px;
          font-size: 14px;
          border-radius: 22px;

          svg {
            width: 16px;
            height: 16px;
          }

          span {
            font-size: 14px;
          }
        }
      }
    }
  }
`;
