import moment from "moment";
import { GIOI_TINH_BY_VALUE } from "constants/index";
import { VALIDATION_RULES, QR_PATTERNS } from "./constants";

export const isUuTien = (ngaySinh) => {
  if (!ngaySinh) return false;

  const tuoi = moment().diff(moment(ngaySinh), "years");
  return (
    tuoi < VALIDATION_RULES.MIN_AGE_PRIORITY ||
    tuoi >= VALIDATION_RULES.MAX_AGE_PRIORITY
  );
};

export const parseBodyHenKham = (data, listAllPhong) => {
  const {
    tenNb,
    maNb,
    gioiTinh,
    ngaySinh,
    cdNoiGioiThieu,
    doiTuong,
    maLoaiDoiTuong,
    maMaDoiTuongKcb,
    maQuocGia,
    maQuocTich,
    maTinhThanhPho,
    soNha,
    maDanToc,
    maN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    soD<PERSON><PERSON><PERSON><PERSON>,
    ten<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    hen<PERSON><PERSON><PERSON><PERSON>,
    lyDo<PERSON><PERSON><PERSON>ham,
    ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    denNgayTheBhyt,
    maTheB<PERSON><PERSON>,
    maMoiDangKy,
    denNgayGiayChuyen,
    tuNgayTheBhyt,
    uuTien,
    maSoGiayToTuyThan,
    ngayCapGiayToTuyThan,
    maKhoa,
    dsDichVu = [],
    maPhongHenKham,
  } = data;

  return {
    tenNb,
    maNb,
    gioiTinh,
    ngaySinh,
    cdNoiGioiThieu,
    doiTuong,
    maLoaiDoiTuong,
    maMaDoiTuongKcb,
    maQuocGia,
    maQuocTich,
    maTinhThanhPho,
    soNha,
    maDanToc,
    maNgheNghiep,
    soDienThoai,
    tenNguoiBaoLanh,
    henKhamLai,
    lyDoDenKham,
    maNoiGioiThieu,
    denNgayTheBhyt,
    maTheBhyt,
    maMoiDangKy,
    denNgayGiayChuyen,
    tuNgayTheBhyt,
    uuTien: isUuTien(ngaySinh),
    maSoGiayToTuyThan,
    ngayCapGiayToTuyThan,
    maKhoa,
    khoaId: listAllPhong.find((item) => item.ma === maPhongHenKham)?.khoaId,
    dsDichVu: dsDichVu,
    loaiGiayToTuyThan: 1,
  };
};

export const formatNumber = (num = 1, length = 4) => {
  return String(num).padStart(length, "0");
};

export const parseQRCode = (value, testMaHoSo, formatMaHoSo) => {
  if (!value) return null;

  const array = value.split("|");

  if (testMaHoSo(value)) {
    return { maHoSo: formatMaHoSo(value) };
  }

  if (QR_PATTERNS.NB_CODE.test(value)) {
    return { maNb: array[4] };
  }

  if (value.endsWith("}")) {
    try {
      return { maNb: JSON.parse(value)?.maNb };
    } catch (error) {
      console.error("Error parsing JSON QR code:", error);
      return null;
    }
  }

  if (VALIDATION_RULES.QR_ARRAY_LENGTHS.includes(array.length)) {
    const date = moment(array[3], "DDMMYYYY");
    const ngayCap = moment(array[6], "DDMMYYYY");
    const diaChi = array[5].toAddress();

    return {
      maSoGiayToTuyThan: array[0],
      otherInfo: {
        tenNb: array[2],
        maSoGiayToTuyThan: array[0],
        ngaySinh: { date: date._d, str: date._d.format("dd/MM/yyyy") },
        tuoi: date._d.getAge() || "",
        gioiTinh: array[4] === GIOI_TINH_BY_VALUE[2] ? 2 : 1,
        soNha: diaChi.soNha,
        diaChi: diaChi.diaChi,
        ngayCapGiayToTuyThan: ngayCap._d,
      },
    };
  }

  return { maSoGiayToTuyThan: array[0] };
};

export const createResetTimeout = (callback, delay = 5000) => {
  return setTimeout(callback, delay);
};

export const clearResetTimeout = (timeoutId) => {
  if (timeoutId) {
    clearTimeout(timeoutId);
  }
};
