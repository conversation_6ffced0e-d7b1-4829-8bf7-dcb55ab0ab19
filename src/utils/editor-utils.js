import pdfUtils from "utils/pdf-utils";
import {
  A4,
  MA_BIEU_MAU_EDITOR,
  LOAI_DICH_VU,
  A3,
  PAGE_TYPE,
  LOAI_IN_BANG_KE_CHI_PHI,
  LOAI_BIEU_MAU,
} from "constants/index";
import { getState } from "redux-store/stores";
import { get, isArray, set } from "lodash";
import { isObject } from "lodash";
import moment from "moment";
import { combineUrlParams } from "utils";
export const MODE = {
  editing: "editing",
  config: "config",
};

//hàm cho phép thêm 1 chuỗi kỹ tự vào tất cả các trường, dùng trong trường hợp thêm mới chuỗi khung2_ vào tất cả các trường.
export const appendPrefix = (data, prefix) => {
  const obj = {};
  Object.keys(data).map((key) => {
    obj[prefix + key] = data[key];
  });
  return obj;
};

export const checkComponentDisable = ({
  itemProps = {},
  mode = "",
  signStatus = {},
  props = {},
  option = {},
}) => {
  // return false;
  let disabled = false;

  // let patientState = patient?.patientState;
  // const FINISHED = 50;
  // const APPOINTMENT_IS_PAID = 10;
  // const APPOINTMENT_NOT_PAID = 20;
  // const OUT_HOSPITAL = 90;
  // const PAID_OUT = 110;
  // const TEMP_OUT = 120;
  // const NULL = 0;
  // const arr = [
  //   FINISHED,
  //   APPOINTMENT_IS_PAID,
  //   APPOINTMENT_NOT_PAID,
  //   OUT_HOSPITAL,
  //   PAID_OUT,
  //   TEMP_OUT,
  //   // NULL,
  // ];

  // const CANCELED = 310;
  // const CANCELING = 320;
  // const HR = 150;
  let state = getState();
  // const arrStatus = [CANCELED, CANCELING, HR];
  if (mode === MODE.editing) {
    // const roleAdmin = authorities.find((item) =>
    //   [
    //     "ROLE_IsofhAdmin",
    //     "ROLE_VpOrganizationAdmin",
    //     "ROLE_AdminTong",
    //     "ROLE_PstwAdmin",
    //     "ROLE_Admin",
    //   ].includes(item)
    // );
    // let { khoaThucHienId, trangThaiDichVu } = state.files?.fileDataHIS || {};
    // if (!isEmpty(state.files?.fileDataHIS) && isEmpty(state.files?.fileData)) {
    //   // debugger;
    // }
    // if (!roleAdmin) {
    //nếu không phải là admin
    // const checkRoleDocument = authorities.includes(
    //   state?.files?.file?.value + ".5"
    // );
    // if (checkRoleDocument) {
    //   disabled = false;
    // } else {
    //   if (
    //     !(
    //       (
    //         (auth.departmentIds || []).includes(patient.khoaId) || //bn ko thuộc khoa người dùng
    //         (auth.departmentIds || []).includes(khoaThucHienId)
    //       ) //hoặc dịch vụ không thuộc khoa người dùng
    //     )
    //   ) {
    // if (!(auth.departmentIds || []).includes(khoaThucHienId)) {
    //   // nếu không cùng khoa thực hiện
    //   if (!isEmpty(state.files?.fileData)) {
    //     //
    //     disabled = true;
    //   }
    // } else
    // disabled = true;
    // if (
    //   (auth.departmentIds || []).includes(patient.khoaId) || //bn thuộc khoa người dùng
    //   (auth.departmentIds || []).includes(khoaThucHienId) //hoặc dịch vụ không thuộc khoa người dùng
    // ) {
    //   if (
    //     arr.includes(patientState) || //nếu trạng thái bệnh nhân thuộc list disable
    //     (arrStatus.includes(trangThaiDichVu) &&
    //       !isEmpty(state.files?.fileData)) //nếu trạng thái dịch vụ thuộc list disable
    //   ) {
    //     disabled = true;
    //   }
    // }
    // }
    // }
    // }
  }

  if (option.isSignature) {
    return disabled;
  }

  disabled =
    disabled ||
    // itemProps.disabled ||
    itemProps.readOnly ||
    props.disable ||
    mode === MODE.config ||
    (Object.keys(signStatus).find((item) => {
      return (
        signStatus[item].block || //block all
        (signStatus[item].chuKy && //hoặc có tồn tại chữ ký và block tại cấp ký trùng với cấu hình tại component
          signStatus[item].levelSign &&
          itemProps.blockSignLevel &&
          signStatus[item].levelSign >= itemProps.blockSignLevel)
      );
    })
      ? true
      : false);

  return disabled;
};

export const combineFields = (json, result = {}, parentKey, getTemplate) => {
  const keys = json ? Object.keys(json) : [];
  keys.forEach((key) => {
    result[parentKey ? `${parentKey}_${key}` : key] =
      json[key] === undefined || json[key] === null ? null : json[key];

    if (isObject(json[key]) && !json[key].length) {
      combineFields(json[key], result, parentKey ? `${parentKey}_${key}` : key);
    }
    if (getTemplate) {
      if (
        isArray(json[key]) &&
        json[key].length &&
        isObject(json[key][0]) &&
        !json[key][0].length
      ) {
        combineFields(
          json[key][0],
          result,
          parentKey ? `${parentKey}_${key}` : key
        );
      }
    }
  });

  return result;
};

export const pageType = {
  from(layoutType, page = "A4") {
    if (
      page === PAGE_TYPE.FORM_A4 ||
      (page !== PAGE_TYPE.FORM_A4 && page !== PAGE_TYPE.FORM_A3)
    ) {
      return layoutType === "default" ? this.A4.v : this.A4.h;
    } else {
      if (page === PAGE_TYPE.FORM_A3) {
        return layoutType === "default" ? this.A3.v : this.A3.h;
      }
    }
  },
  A3: {
    v: {
      name: "A3",
      landscape: false,
      width: A3.width,
      height: A3.height,
    },
    h: {
      name: "A3",
      landscape: true,
      width: A3.height,
      height: A3.width,
    },
  },
  A4: {
    v: {
      name: "A4",
      landscape: false,
      width: A4.width,
      height: A4.height,
    },
    h: {
      name: "A4",
      landscape: true,
      width: A4.height,
      height: A4.width - 1,
    },
  },
  A5: {
    v: {
      name: "A5",
      landscape: false,
      width: A4.height / 2,
      height: A4.width,
    },
    h: {
      name: "A5",
      landscape: true,
      width: A4.width,
      height: A4.height / 2,
    },
  },
};

export const pdfGenerator = ({
  layout,
  resultHml,
  htmlNotEdit,
  ma,
  fromHeadless,
  pageType,
}) => {
  return new Promise(async (resolve, reject) => {
    try {
      const files = Array.from(document.getElementsByClassName("form-content"));
      const promises = [];
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        promises.push(
          getHtml({
            file,
            layout,
            resultHml,
            htmlNotEdit,
            ma,
            fromHeadless,
            pageType: ["A3", "A4"].includes(pageType) ? pageType : "A4",
          })
        );
      }
      Promise.all(promises)
        .then((s) => {
          if (resultHml) resolve(s);
          else resolve({ pdfUrls: s.filter((item) => item) });
        })
        .catch((e) => {
          console.log(e);
          resolve({ pdfUrls: [] });
        });
    } catch (error) {
      console.log(error);
      reject(error);
    }
  });
};

export const getHtml = async ({
  file,
  layout,
  resultHml,
  htmlNotEdit,
  ma,
  fromHeadless,
  pageType = "A4",
}) => {
  let css = `
    .watermark{
      display: none !important;
    }
    #watermarkPattern{
      display: none !important;
    }
  `;
  const html = window.editorGetHtmlTag
    ? window.editorGetHtmlTag(document)
    : document.getElementsByTagName("html")[0].cloneNode(true);
  html.classList.remove("browser");
  html.classList.add("print-pdf");
  const formContent = document.querySelector(".form-content");

  const componentPage = file.getElementsByClassName("component-page")[0];

  const configNotEditHtml =
    htmlNotEdit ||
    formContent.getAttribute("data-type-not-edit-html") === "true";
  if (configNotEditHtml) {
    const style = document.createElement("style");
    let marginTop = 30;
    let marginBottom = 30;
    if (componentPage) {
      marginTop = componentPage.getAttribute("data-page-top") || 30;
      marginBottom = componentPage.getAttribute("data-page-bottom") || 30;
    }
    style.textContent = `
    @page {
        margin-top: ${marginTop}px;
        margin-bottom: ${marginBottom}px;
    }
    ${css}
`;
    html.appendChild(style);

    try {
      const formContent = document
        .getElementsByClassName("form-content")[0]
        .cloneNode(true);
      if (fromHeadless) {
        formContent.classList.add("editor-loaded-data");
        formContent.setAttribute(
          "data-layout",
          JSON.stringify({
            format: layout?.name || "A4",
            landscape: layout.landscape,
            margin: {},
          })
        );
      }
      const body = html.getElementsByTagName("body")[0];
      const canvasVitalsigns =
        document.getElementsByClassName("canvas-vital-signs");
      for (let j = 0; j < canvasVitalsigns.length; j++) {
        const vitalsigns = canvasVitalsigns[j];
        let id = vitalsigns.getAttribute("id");
        const vitalsigns1 = formContent.querySelector(`[id="${id}"]`);
        if (vitalsigns1) {
          vitalsigns1.innerHTML = "";
          const canvas = vitalsigns.getElementsByTagName("canvas");
          for (let i = 0; i < canvas.length; i++) {
            let img = document.createElement("img"); // Create a <button> element
            img.setAttribute("src", canvas[i].toDataURL());
            img.style.cssText = canvas[i].style.cssText;
            vitalsigns1.appendChild(img);
          }
        }
      }
      const canvasPdfs = document.getElementsByClassName("view-pdf-component");
      for (let j = 0; j < canvasPdfs.length; j++) {
        const pdfFile = canvasPdfs[j];
        if (pdfFile) {
          // pdfFile.innerHTML = "";
          const elementPdf =
            formContent.querySelectorAll(`.view-pdf-component`)[j];
          if (elementPdf) {
            elementPdf.innerHTML = "";
            const canvas = pdfFile.getElementsByTagName("canvas");
            for (let i = 0; i < canvas.length; i++) {
              let img = document.createElement("img"); // Create a <button> element
              img.setAttribute("src", canvas[i].toDataURL());
              img.style.cssText = canvas[i].style.cssText;
              elementPdf.appendChild(img);
            }
          }
        }
      }
      body.innerHTML = "";
      if (ma == "EMR_HSDD043.1") {
        const formContentHeight =
          document.getElementsByClassName("form-content")[0]?.offsetHeight;

        if (formContentHeight > 1155 && formContentHeight < 1170) {
          const spanNode = document.createElement("div");
          spanNode.style.height = "15px";
          body.appendChild(spanNode);
        }
      }

      body.append(formContent);

      const modalLoading = html.getElementsByClassName("ant-modal-root");
      if (modalLoading?.length) {
        modalLoading[0].remove();
      }
      if (resultHml) {
        return html.outerHTML;
      }

      if (["EMR_BA077.1", "EMR_BA077", "EMR_BA503"].includes(ma)) {
        var script = document.createElement("script");
        script.text = "window.scrollTo(0, 500);";
        html.append(script);
      }

      if (resultHml) return html.outerHTML;
      const blob = await pdfUtils.htmlToPdf(html.outerHTML, {
        format: pageType,
        landscape: layout.landscape,
        margin: {},
      });
      const blobUrl = window.URL.createObjectURL(blob);

      return blobUrl;
    } catch (error) {
      console.log(error);
      return;
    }
  }

  let body = html.getElementsByTagName("body")[0];
  let head = html.getElementsByTagName("head")[0];
  debugger;

  // if (!fromHeadless) css = await pullAllCss(html);
  let style = document.createElement("style");
  css = css + (await pullAllCss(html));
  if (html.querySelector("[data-name-file='EMR_BA252']")) {
    css += "@page {margin-right: 5mm}";
  }
  if (head) {
    head.appendChild(style);
    style.type = "text/css";
    if (style.styleSheet) {
      // This is required for IE8 and below.
      style.styleSheet.cssText = css;
    } else {
      style.appendChild(document.createTextNode(css));
    }
  }
  const wrapElm = document.createElement("div");
  wrapElm.setAttribute("class", "print-wrapper");
  wrapElm.setAttribute("id", "print-wrapper");
  let margin = {};
  if (componentPage) {
    margin.top = componentPage.getAttribute("data-page-top") || 0;
    margin.bottom = componentPage.getAttribute("data-page-bottom") || 0;
    margin.left = componentPage.getAttribute("data-page-left") || 0;
    margin.right = componentPage.getAttribute("data-page-right") || 0;
  }
  let lines = [];
  let printArea = document.createElement("div");
  printArea.setAttribute("class", "view-file-mode");
  printArea.style.fontFamily = `font-family: "Times New Roman", sans-serif;`;
  printArea.style.color = "black";
  const pageTemplate = file.cloneNode(true);
  if (pageTemplate?.childNodes[0]) {
    pageTemplate.childNodes[0].innerHTML = null;
  }
  let componenFooter = null;
  let componentHeader = null;
  Array.from(file.childNodes[0].childNodes).forEach((itemLv1) => {
    itemLv1.childNodes.forEach((itemLv2) => {
      if (itemLv2.getAttribute("data-component") !== "footerAndHeader") {
        lines = [...lines, itemLv2];
      } else {
        if (itemLv2.querySelector("[data-type-header='true']")) {
          componentHeader = itemLv2;
        } else {
          componenFooter = itemLv2;
        }
      }
    });
  });
  if (componenFooter) {
    componenFooter.style.position = "fixed";
    componenFooter.style.left = "0px";
    componenFooter.style.bottom = "0px";
    componenFooter.style.zIndex = 10000;
  }
  generate({
    lines,
    printArea,
    height: 0,
    pageTemplate,
    layout,
    file,
    margin,
    componenFooter,
    componentHeader,
    fromHeadless,
  });
  wrapElm.append(printArea);
  if (fromHeadless) {
    wrapElm.classList.add("editor-loaded-data");
    wrapElm.setAttribute(
      "data-layout",
      JSON.stringify({
        format: layout?.name || "A4",
        landscape: layout?.landscape,
        margin: {},
      })
    );
  }

  if (wrapElm) {
    if (body) {
      body.innerHTML = wrapElm.outerHTML;
    }
    const canvasVitalsigns =
      document.getElementsByClassName("canvas-vital-signs");

    for (let j = 0; j < canvasVitalsigns.length; j++) {
      const vitalsigns = canvasVitalsigns[j];
      let id = vitalsigns.getAttribute("id");
      const vitalsigns1 = html.querySelector(`[id="${id}"]`);
      if (vitalsigns1) {
        vitalsigns1.innerHTML = "";
        const canvas = vitalsigns.getElementsByTagName("canvas");
        for (let i = 0; i < canvas.length; i++) {
          let img = document.createElement("img"); // Create a <button> element
          img.setAttribute("src", canvas[i].toDataURL());
          img.style.cssText = canvas[i].style.cssText;
          vitalsigns1.appendChild(img);
        }
      }
    }

    const canvasPdfs = document.getElementsByClassName("view-pdf-component");
    for (let j = 0; j < canvasPdfs.length; j++) {
      const pdfFile = canvasPdfs[j];
      if (pdfFile) {
        // pdfFile.innerHTML = "";
        const elementPdf = html.querySelectorAll(`.view-pdf-component`)[j];
        if (elementPdf) {
          elementPdf.innerHTML = "";
          const canvas = pdfFile.getElementsByTagName("canvas");
          for (let i = 0; i < canvas.length; i++) {
            let img = document.createElement("img"); // Create a <button> element
            img.setAttribute("src", canvas[i].toDataURL());
            img.style.cssText = canvas[i].style.cssText;
            elementPdf.appendChild(img);
          }
        }
      }
    }
    try {
      if (resultHml) {
        return html.outerHTML;
      } else {
        html.querySelectorAll(".hiddenPdf").forEach((el) => {
          const textEl = (el.textContent.split(":") || [])[1]?.trim();
          if (!textEl) {
            el.innerHTML = "";
          }
        });
        const blob = await pdfUtils.htmlToPdf(html.outerHTML, {
          format: layout.name || "A4",
          landscape: layout.landscape,
          margin: {},
        });
        const blobUrl = window.URL.createObjectURL(blob);
        return blobUrl;
      }
    } catch (error) {
      return null;
    }
  }
};
export const generate = ({
  lines,
  printArea,
  height = 0,
  pageTemplate,
  layout = pageType.A4.v,
  file,
  margin,
  componenFooter,
  componentHeader,
  fromHeadless,
}) => {
  let LAYOUT_HEIGHT =
    layout === "default" ? pageType.A4.v.height : layout.height;
  if (componenFooter && componentHeader) {
    LAYOUT_HEIGHT =
      LAYOUT_HEIGHT -
      componentHeader?.clientHeight -
      componenFooter?.clientHeight;
  } else if (componenFooter || componentHeader) {
    LAYOUT_HEIGHT =
      LAYOUT_HEIGHT - componentHeader
        ? componentHeader?.clientHeight
        : componenFooter?.clientHeight;
  }
  const top = margin.top;
  const bottom = margin.bottom;
  const PAGE_HEIGHT = LAYOUT_HEIGHT;
  const PAGE_AREA_HEIGHT = LAYOUT_HEIGHT - top - bottom;
  const createPage = (parrent, fullHeight) => {
    let height = 0;
    const page = pageTemplate.cloneNode(true);
    let className = page.getAttribute("class");
    className += " page-inside "; // + (isHorizontal ? "landscape" : "");
    if (layout.landscape) {
      className += " landscape ";
    }
    if (fullHeight) {
      page.style.height = "100%";
      className += " full-height ";
    }
    page.setAttribute("class", className);
    parrent.append(page);
    // page.style.padding = `${margin.top}px ${margin.right}px ${margin.bottom}px ${margin.left}px`;
    if (componentHeader) {
      page.childNodes[0].append(componentHeader.cloneNode(true));
      height = componentHeader.clientHeight;
    }
    if (componenFooter) {
      page.childNodes[0].append(componenFooter.cloneNode(true));
    }
    page.childNodes[0].style.position = "relative";
    page.childNodes[0].style.width =
      layout === "default" || !layout ? `${pageType.A4.v.width}px` : layout;

    return { height, page: page.childNodes[0] };
  };

  const appendTr = (
    trs = [],
    itemTemplate,
    line,
    height,
    page,
    printArea,
    initTableHeight = 0,
    headerTable
  ) => {
    let newLine = null;
    let tbody = null;
    const getRowSpanNumber = (tr) => {
      const tds = Array.from(tr.getElementsByTagName("td"));
      let tdsRowSpan = tds
        .filter((td) => {
          //check xem trong dòng đó có thuộc tính rowspan không
          return td.attributes["rowspan"]?.value;
        })
        .map((td) => td.attributes["rowspan"]?.value);
      let maxRowSpan = Math.max(tdsRowSpan); //kiểm tra xem rowspan tối đa là bao nhiêu
      return maxRowSpan;
    };
    trs.forEach((tr, index) => {
      let maxRowSpan = getRowSpanNumber(tr);
      let needBreakPage = false; //thêm biến đánh dấu cần break page;
      let newHeight = height;
      for (let i = index; i <= index + maxRowSpan && i < trs.length; i++) {
        const maxRowSpan2 =
          getRowSpanNumber(trs[i]) - (maxRowSpan - (i - index)); //
        if (maxRowSpan2 > 0) {
          maxRowSpan += maxRowSpan2;
        }

        //duyệt qua danh sách các row trong phạm vi rowspan
        if (newHeight + trs[i].offsetHeight > PAGE_HEIGHT) {
          //check nếu dòng đó vượt quá trang
          needBreakPage = true; //thì đánh dấu là true
          break;
        }
        newHeight += trs[i].offsetHeight;
      }
      // if (tdsRowSpan?.length)
      if (height + tr.offsetHeight > PAGE_HEIGHT || needBreakPage) {
        const newPage = createPage(printArea);
        page = newPage.page;
        newLine = null;
        height = initTableHeight;
        tbody = null;
      }
      if (!newLine) {
        newLine = line.cloneNode();
        page.append(newLine);
      }
      if (!tbody) {
        let table = itemTemplate.cloneNode(true);
        newLine.append(table);
        tbody = table.getElementsByTagName("tbody")[0];
        if (headerTable) {
          tbody.append(headerTable);
        }
      }
      if (newLine && tbody) {
        height += tr.offsetHeight;
        tbody.append(tr.cloneNode(true));
      }
    });

    return { nPage: page, nHeight: height };
  };

  const appendTable = (
    table,
    line,
    item,
    height,
    page,
    printArea,
    headerTable
  ) => {
    let tbody = item.getElementsByTagName("tbody");
    let initTableHeight = 0;
    if (tbody?.length) {
      initTableHeight = item.clientHeight - tbody[0].clientHeight;
      height += initTableHeight;
    }
    let itemTemplate = item.cloneNode(true);
    tbody = itemTemplate.getElementsByTagName("tbody");
    if (tbody?.length) {
      tbody[0].innerHTML = "";
    }
    let trs = Array.from(table.getElementsByTagName("tr"));
    return appendTr(
      trs,
      itemTemplate,
      line,
      height,
      page,
      printArea,
      initTableHeight,
      headerTable
    );
  };
  const removeHiddenElement = (line) => {
    const listLine = line.querySelectorAll("[data-type='line']");
    // Nêu trong line nhiều line con thì lọc qua từng line bên trong
    if (listLine.length) {
      listLine.forEach((l) => {
        removeHiddenElement(l);
      });
    } else {
      const elementHidden = line.querySelectorAll(".hidden-element");
      // Lấy tất cả các phần tử hidden-element trong dòng
      // Nếu line có 1 phần element hidden-element hoặc tất cả các element hidden-element thì dòng sẽ bị ẩn
      if (
        (elementHidden.length && line.childNodes.length === 1) ||
        elementHidden.length === line.childNodes.length
      ) {
        line.style.display = "none";
      }
      if (
        elementHidden.length &&
        line.childNodes.length > 1 &&
        elementHidden.length !== line.childNodes.length
      ) {
        elementHidden.forEach((element) => {
          element.style.display = "none";
        });
      }
    }
  };
  class GridPage {
    clLine = null;
    clBlock = null;
    clComponent = null;
    clGrid = null;
    createTemplate = ({
      page,
      component,
      printArea,
      line,
      item,
      fullHeight,
    }) => {
      if (!page) {
        const newPage = createPage(printArea, fullHeight);
        page = newPage.page;
      }
      if (!this.clLine) {
        this.clLine = line.cloneNode();
        page.append(this.clLine);
      }
      if (!this.clBlock) {
        this.clBlock = item.cloneNode();
        this.clLine.append(this.clBlock);
      }
      if (!this.clComponent) {
        this.clComponent = component.cloneNode();
        this.clBlock.append(this.clComponent);
      }
      if (!this.clGrid) {
        this.clGrid = component.childNodes[0].cloneNode();
        this.clComponent.append(this.clGrid);
      }
      return page;
    };

    breakPage = ({
      page,
      component,
      printArea,
      line,
      item,
      fullHeight,
      isToDieuTri,
    }) => {
      this.clLine = null;
      this.clBlock = null;
      this.clComponent = null;
      this.clGrid = null;
      page = this.createTemplate({
        page: null,
        component,
        printArea,
        line,
        item,
        fullHeight,
      });
      return [page, 0];
    };
  }

  const phieuKhac = ({ item, line, height, page, printArea, newLine }) => {
    const gridPage = new GridPage();
    const component = item.childNodes[0];
    if (component?.getAttribute("data-type") == "page") {
      const top = component.getAttribute("data-page-top");
      const bottom = component.getAttribute("data-page-bottom");
      const PAGE_AREA_HEIGHT = PAGE_HEIGHT - top - bottom;
      //thì lấy ra grid trong page/layout
      const grid = component.childNodes[0];
      //lây ra tất cả các line con trong grid
      const listChildLines = Array.from(grid.childNodes);
      listChildLines.forEach((cLine, cIndex) => {
        //duyệt qua tất cả các line con trong page/layout
        if (cLine.clientHeight + height < PAGE_AREA_HEIGHT) {
          //kiểm tra xem có thể chèn vào page hiện tại không, nếu có thì thực hiện append vào
          page = gridPage.createTemplate({
            page,
            component,
            printArea,
            line,
            item,
          });
          const cLineClone = cLine.cloneNode(true);
          removeHiddenElement(cLineClone);
          gridPage.clGrid.append(cLineClone);
          height += cLine.clientHeight;
        } else {
          //ngược lại thì phải ngắt page
          [page, height] = gridPage.breakPage({
            page,
            component,
            printArea,
            line,
            item,
          });
          const cLineClone = cLine.cloneNode(true);
          removeHiddenElement(cLineClone);
          gridPage.clGrid.append(cLineClone);
          height = 0;
        }
      });
      return { nHeight: height, nPage: page, needReturn: true, newLine };
    } else {
      let table = item.getElementsByTagName("tbody")[0];
      let headerTable = table?.querySelector("[data-type='header']");
      if (headerTable) {
        headerTable = headerTable.cloneNode(true);
      }

      if (table) {
        let { nPage, nHeight } = appendTable(
          table,
          line,
          item,
          height,
          page,
          printArea,
          headerTable
        );
        if (nPage) {
          page = nPage;
          height = nHeight;
          newLine = null;
          return { nPage, nHeight, needReturn: true, newLine };
        } else {
          if (height != 0) {
            //nếu page hiện tại có phần tử rồi thì bắt buộc tạo 1 page mới
            const newPage = createPage(printArea);
            page = newPage.page; //tao moi 1 page moi
            height = newPage.height;
          } else {
            newLine = null; //tao moi 1 line moi
            height = 0; //reset height
          }
        }
      } else {
        //neu item height vượt quá
        const newPage = createPage(printArea);
        page = newPage.page; //tao moi 1 page moi
        newLine = null; //tao moi 1 line moi
        height = newPage.height; //reset height
      }
      return { nPage: page, nHeight: height, needReturn: false, newLine };
    }
  };
  const phieuTienGayMe = ({ item, line, height, page, printArea }) => {
    const gridPage = new GridPage();
    //nếu là bàn giao thuốc thì nếu page đã có content thì mặc định tạo mới page
    if (height != 0) {
      page = createPage(printArea);
      height = 0;
    }
    //lấy ra component trong block

    const component = item.childNodes[0];
    if (component) {
      //nếu component là page hoặc layout
      if (["page", "layout"].includes(component.getAttribute("data-type"))) {
        //thì lấy ra grid trong page/layout
        const grid = component.childNodes[0];
        //lây ra tất cả các line con trong grid
        const listChildLines = Array.from(grid.childNodes);
        listChildLines.forEach((cLine, cIndex) => {
          //duyệt qua tất cả các line con trong page/layout
          if (
            //nếu cline không chứa component gây mê
            cLine.getAttribute("data-component") !== "Table"
          ) {
            if (cLine.clientHeight + height < PAGE_HEIGHT) {
              //kiểm tra xem có thể chèn vào page hiện tại không, nếu có thì thực hiện append vào
              page = gridPage.createTemplate({
                page,
                component,
                printArea,
                line,
                item,
              });
              gridPage.clGrid.append(cLine.cloneNode(true));
              height += cLine.clientHeight;
            } else {
              //ngược lại thì phải ngắt page
              [page, height] = gridPage.breakPage({
                page,
                component,
                printArea,
                line,
                item,
              });
              gridPage.clGrid.append(cLine.cloneNode(true));
              height = 0;
              page = null;
            }
          } else {
            //nếu line đó chưa component gây mê
            //thì tạo template line và clear html của line đó
            const templateChildLine = cLine.cloneNode(true);
            const tableBanGiao = cLine
              .querySelector("[data-type='block']")
              .querySelector("[data-type='table']")
              .querySelector("[data-type='table-normal-render']");
            templateChildLine.querySelector("tbody").innerHTML = "";
            let tbody = null;
            const trs = Array.from(
              tableBanGiao.querySelector("tbody").childNodes
            );
            let templateChildLineX = null;
            const addEmptyTable = () => {
              if (!templateChildLineX) {
                templateChildLineX = templateChildLine.cloneNode(true);
                gridPage.clGrid?.append(templateChildLineX);
              }
              if (!tbody) tbody = templateChildLineX.querySelector("tbody");
            };
            let breakPage = false;
            trs.forEach((tr, trIndex) => {
              const className = tr.getAttribute("class");
              page = gridPage.createTemplate({
                page,
                component,
                printArea,
                line,
                item,
              });
              if (className !== "header") {
                if (
                  (trIndex == 0 && tr.clientHeight + height > PAGE_HEIGHT) ||
                  tr.clientHeight + height > PAGE_HEIGHT
                ) {
                  if (!breakPage) {
                    [page, height] = gridPage.breakPage({
                      page,
                      component,
                      printArea,
                      line,
                      item,
                    });
                    templateChildLineX = null;
                    height = 0;
                    tbody = null;
                  }
                } else {
                  breakPage = false;
                }
              } else {
                if (tr.childNodes.length === 1) {
                  tr.childNodes[0].style.maxWidth = "unset";
                }
                if (
                  tr.clientHeight + height + trs[trIndex + 1].clientHeight >
                  PAGE_HEIGHT
                ) {
                  [page, height] = gridPage.breakPage({
                    page,
                    component,
                    printArea,
                    line,
                    item,
                    fullHeight:
                      trs[trIndex + 1].clientHeight + tr.clientHeight >
                      PAGE_HEIGHT,
                  });
                  breakPage = true;
                  templateChildLineX = null;
                  height = 0;
                  tbody = null;
                } else {
                  breakPage = false;
                }
              }
              addEmptyTable();
              tbody.append(tr.cloneNode(true));
              height = height + tr.clientHeight;
            });
          }
        });
      }
    }
    return { nHeight: height, nPage: page };
  };
  const toDieuTri = ({
    item,
    line,
    height,
    page,
    printArea,
    fromHeadless,
    offsetHeight,
  }) => {
    const gridPage = new GridPage();
    //nếu là bàn giao thuốc thì nếu page đã có content thì mặc định tạo mới page;
    const value = item
      .cloneNode(true)
      .querySelector(".to-dieu-tri")
      ?.getAttribute("data-value-stt");
    if (fromHeadless) {
      height += offsetHeight;
      page.append(line.cloneNode(true));
    } else if (height !== 0) {
      page = createPage(printArea);
      height = 0;
    }

    //lấy ra component trong block
    const component = item.childNodes[0];
    if (component && !fromHeadless) {
      //nếu component là page hoặc layout
      if (["page", "layout"].includes(component.getAttribute("data-type"))) {
        //thì lấy ra grid trong page/layout
        const grid = component.childNodes[0];
        //lây ra tất cả các line con trong grid
        const listChildLines = Array.from(grid.childNodes);
        listChildLines.forEach((cLine, cIndex) => {
          //duyệt qua tất cả các line con trong page/layout
          const headerForm = cLine.querySelector(".header-form");

          //nếu line đó chưa component gây mê
          //thì tạo template line và clear html của line đó
          let listToDieuTri = cLine.querySelector("[data-type='to-dieu-tri']");
          if (listToDieuTri) {
            Array.from(listToDieuTri.childNodes).forEach(
              (toDieuTri, indextoDieuTri) => {
                height = headerForm.clientHeight;
                const tableToDieuTri = toDieuTri.querySelector(
                  "[data-type='table-to-dieu-tri']"
                );
                const templateChildLine = toDieuTri.cloneNode(true);
                templateChildLine
                  .querySelector('[data-type="table-to-dieu-tri"]')
                  .querySelector(".table-tbody").innerHTML = "";
                const thead = tableToDieuTri.querySelector(".table-head");
                const tHeadHeight = thead.clientHeight;
                let tbody = null;
                const trs = Array.from(
                  tableToDieuTri.querySelector(".table-tbody").childNodes
                );
                let templateChildLineX = null;

                const addEmptyTable = (removeHeader) => {
                  if (!templateChildLineX) {
                    templateChildLineX = templateChildLine.cloneNode(true);
                    if (removeHeader) {
                      const header =
                        templateChildLineX.querySelector(".header-form");
                      if (header) {
                        header.innerHTML = "";
                      }
                    }
                    gridPage.clGrid.append(templateChildLineX);
                    height += tHeadHeight;
                  }
                  if (!tbody)
                    tbody = templateChildLineX.querySelector(".table-tbody");
                };
                let countPage = 1;

                trs.forEach((tr, trIndex) => {
                  page = gridPage.createTemplate({
                    page,
                    component,
                    printArea,
                    line,
                    item,
                  });
                  tr.querySelectorAll(".hiddenPdf").forEach((el) => {
                    const textEl = (el.textContent.split(":") || [])[1]?.trim();
                    if (!textEl) el.remove();
                  });
                  let removeHeader = false;
                  if (
                    trIndex !== 0 &&
                    tHeadHeight + tr.clientHeight + height > PAGE_HEIGHT - 55
                  ) {
                    countPage = countPage + 1;
                    if (
                      countPage % +value !== 1 &&
                      countPage > 1 &&
                      value != 1
                    ) {
                      removeHeader = true;
                    }
                    [page, height] = gridPage.breakPage({
                      page,
                      component,
                      printArea,
                      line,
                      item,
                      fullHeight: false,
                    });
                    templateChildLineX = null;

                    height = removeHeader ? 0 : headerForm.clientHeight;
                    tbody = null;
                  } else {
                    removeHeader = false;
                  }

                  addEmptyTable(removeHeader);
                  tbody.append(tr.cloneNode(true));
                  height = height + tr.clientHeight;
                });
              }
            );
          }
        });
      }
    }

    return { nHeight: height, nPage: page };
  };
  const appendLine = (line, height, page, printArea, fromHeadless) => {
    if (!page || line.getAttribute("data-component") == "Page") {
      //nếu page null hoặc line đó chưa component Page thì tạo page mới.
      const newPage = createPage(printArea);
      page = newPage.page;
      height = newPage.height;
    }
    let childNodes = Array.from(line.childNodes);
    // childNodes.forEach((item, index) => {

    // }
    if (height + line.offsetHeight <= PAGE_HEIGHT) {
      //cộng line vào page hiện taị nếu chưa vượt quá
      const lineClone = line.cloneNode(true);
      const grid = lineClone?.querySelector("[data-type='grid']");
      if (grid) {
        grid.childNodes.forEach((lineChild) => {
          removeHiddenElement(lineChild);
        });
      }
      page.append(lineClone);
      height += line.offsetHeight;
      return {
        nHeight: height,
        nPage: page,
      };
    } //trường hợp vượt quá
    else {
      let newLine; //clone ra 1 line rỗng
      //  childNodes = Array.from(line.childNodes); //get all các block trong line
      childNodes.forEach((item, index) => {
        if (height + item.offsetHeight > PAGE_HEIGHT) {
          if (item.querySelector("[data-name-file='EMR_BA252']")) {
            return phieuTienGayMe({ item, line, height, page, printArea });
          } else if (item.querySelector("[data-type='to-dieu-tri']")) {
            return toDieuTri({
              item,
              line,
              height,
              page,
              printArea,
              fromHeadless,
              offsetHeight: item.offsetHeight,
            });
          } else {
            const { nPage, nHeight, needReturn, ...rest } = phieuKhac({
              item,
              line,
              height,
              page,
              printArea,
              newLine,
            });
            page = nPage;
            height = nHeight;
            newLine = rest.newLine;
            if (needReturn) return;
          }
          // }
        }
        if (!newLine) {
          newLine = line.cloneNode();
          page.append(newLine);
        }
        newLine.append(item.cloneNode(true)); //thêm item vào line
        height += item.offsetHeight; //update current height;
        if (height > PAGE_HEIGHT) height = height - PAGE_HEIGHT;
      });
      return {
        nHeight: height,
        nPage: page,
      };
    }
  };
  let index = 0;
  let page = null;
  do {
    if (lines[index]) {
      const { nHeight, nPage } = appendLine(
        lines[index],
        height,
        page,
        printArea,
        fromHeadless
      );
      page = nPage;
      height = nHeight;
      index += 1;
    }
  } while (lines[index]);
  let paragraphs = printArea.getElementsByClassName("editing-content");
  let listPage = printArea.getElementsByClassName("page-inside");
  Array.from(listPage).forEach((item) => {
    let page = item.getElementsByClassName("component-page");
    if (page?.length) {
      item.classList.add("full-page");
    }
  });
  for (var i = paragraphs.length - 1; i >= 0; --i) {
    if (
      paragraphs[i].getAttribute("contenteditable") == "true" &&
      !paragraphs[i].innerText
    )
      paragraphs[i].remove();
  }
};

export const pullFileCss = async (file) => {
  return new Promise((resolve, reject) => {
    try {
      fetch(file)
        .then((s) => {
          if (s.status === 200) return s.text();
          resolve("");
        })
        .then((text) => {
          resolve(text);
        })
        .catch((e) => {
          resolve("");
        });
    } catch (error) {
      resolve("");
    }
  });
};

export const pullAllCss = async (html) => {
  try {
    let allStyle = Array.from(html.getElementsByTagName("link"))
      .filter((item) => item && item?.href?.indexOf(".css") !== -1)
      .map((item) => {
        return pullFileCss(item?.href);
      });
    return (
      (await Promise.all(allStyle).then((values) => values.join("\n\r"))) || ""
    );
  } catch (error) {
    return "";
  }
};

export const getValueForm = (form, key) => {
  try {
    if (!key) return null;
    if (!form) return null;
    if (form.hasOwnProperty(key)) return form[key];
    const path = String(key).replace(/_/g, ".");
    return get(form, path);
  } catch (error) {
    return null;
  }
};
export const setValueForm = (form, key, value) => {
  try {
    if (!form || !key) return;
    if (form.hasOwnProperty(key)) form[key] = value;
    const path = String(key).replace(/_/g, ".");
    return set(form, path, value);
  } catch (error) {}
};

export const convert = (keys) => {
  let result = {};
  for (const key in keys) {
    try {
      const value = keys[key];
      const path = key.replace(/_/g, "."); // thay tất cả _ thành .
      set(result, path, value === undefined || value === null ? null : value);
      // let arr = key.split("_");
      // let obj = result;
      // arr.forEach((item, index) => {
      //   if (index !== arr.length - 1) {
      //     if (!obj[item]) {
      //       obj[item] = {};
      //     }
      //     obj = obj[item];
      //   } else {
      //     obj[item] = value === undefined || value === null ? null : value;
      //   }
      // });
    } catch (error) {}
  }
  return result;
};

export const getLayout = (layoutType = "default", pageType = "A4") => {
  let layout = { width: A4.width, height: A4.height };
  switch (pageType) {
    case "A4":
      layout = {
        width: layoutType === "default" ? 838 : 1185,
        height: layoutType === "default" ? 1184 : 837,
      };
      break;
    case "A3":
      layout = {
        width: layoutType === "default" ? A3.width : A3.height,
        height: layoutType === "default" ? A3.height : A3.width,
      };
      break;
    default:
      layout = {
        width: layoutType === "default" ? 838 : 1185,
        height: layoutType === "default" ? 1184 : 837,
      };
  }
  return layout;
};
export const addFooterPage = async ({
  dataPrint,
  urls,
  showSdt = false,
  khongHienThiNguoiIn,
}) => {
  try {
    let pageNumber = 1;
    const {
      pdf: mergedPdf,
      PDFDocument,
      rgb,
      fonts,
    } = await pdfUtils.createPdf([
      {
        fontName: "fontBold",
        url: "/fonts/font-times-new-roman/SVN-Times_New_Roman_2_italic.ttf",
      },
      {
        fontName: "fontItalic",
        url: "/fonts/font-times-new-roman/SVN-Times_New_Roman_Bold_Italic.ttf",
      },
    ]);
    for (const pdfCopyDoc of urls) {
      const pdfBytes = await fetch(pdfCopyDoc).then((res) => res.arrayBuffer());
      const pdf = await PDFDocument.load(pdfBytes);

      const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
      copiedPages.forEach((page) => {
        page.drawText("ISOFH", {
          x: 15,
          y: 25,
          size: 8,
          font: fonts.fontBold,
          color: rgb(0, 0, 0),
        });
        let tenNguoiIn = `${
          dataPrint.tenNguoiIn && !khongHienThiNguoiIn
            ? ` - Người in - ${dataPrint.tenNguoiIn}`
            : " "
        }`;
        let soDienThoai = `${
          dataPrint.soDienThoai
            ? ` - Số điện thoại người bệnh: ${dataPrint.soDienThoai}`
            : " "
        }`;
        page.drawText(
          `${showSdt ? soDienThoai : tenNguoiIn} - Ngày in: ${
            dataPrint.thoiGianIn
              ? moment(dataPrint.thoiGianIn).format("DD/MM/YYYY HH:mm")
              : moment(new Date()).format("DD/MM/YYYY HH:mm")
          }`,
          {
            x: 40,
            y: 25,
            size: 8,
            font: fonts.fontItalic,
            color: rgb(0, 0, 0),
          }
        );
        page.drawText(`${pageNumber}/${copiedPages.length}`, {
          x: page.getWidth() - 30,
          y: 25,
          size: 8,
          font: fonts.timesRoman,
          color: rgb(0, 0, 0),
        });
        mergedPdf.addPage(page);
        pageNumber++;
      });
    }
    const mergedPdfFile = await mergedPdf.save();
    const blob = new Blob([mergedPdfFile], { type: "application/pdf" });
    return window.URL.createObjectURL(blob);
  } catch (error) {
    console.log("error", error);
  }
};
export const stripHtml = (html) => {
  let tmp = document.createElement("DIV");
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || "";
};

export const getLinkHeadless = (props) => {
  const {
    ma,
    nbDotDieuTriId,
    nbGoiDvId,
    lichSuKyId,
    mhParams,
    soPhieu,
    nbDvKhamId,
    thoiGianThucHien,
    khoaChiDinhId,
    baoCaoId,
    bienBanHoiChanId,
    chiDinhTuDichVuId,
    chiDinhTuLoaiDichVu,
    conThu,
    loaiBieuMau,
    maBaoCao,
  } = props;
  if (window.getLinkHeadless) window.getLinkHeadless(props);
  if (loaiBieuMau == LOAI_BIEU_MAU.BIEU_MAU_CHI_XEM) return null;

  switch (ma) {
    case "P035":
    case "P037":
    case "P038":
    case "P039":
    case "P042":
    case "P043":
    case "P056":
    case "P074":
    case "P077":
    case "P079":
    case "P090":
    case "P091":
    case "P093":
    case "P095":
    case "P097":
    case "P098":
    case "P099":
    case "P100":
    case "P101":
    case "P102":
    case "P105":
    case "P106":
    case "P111":
    case "P113":
    case "P115":
    case "P116":
    case "P117":
    case "P118":
    case "P119":
    case "P120":
    case "P122":
    case "P123":
    case "P124":
    case "P125":
    case "P126":
    case "P127":
    case "P129":
    case "P131":
    case "P141":
    case "P146":
    case "P150":
    case "P153":
    case "P154":
    case "P155":
    case "P156":
    case "P157":
    case "P162":
    case "P163":
    case "P172":
    case "P173":
    case "P174":
    case "P175":
    case "P179":
    case "P180":
    case "P196":
    case "P213":
    case "P232":
    case "P240":
    case "P241":
    case "P251":
    case "P256":
    case "P257":
    case "P274":
    case "P283":
    case "P284":
    case "P285":
    case "P286":
    case "P295":
    case "P296":
    case "P297":
    case "P299":
    case "P310":
    case "P311":
    case "P312":
    case "P313":
    case "P314":
    case "P315":
    case "P316":
    case "P317":
    case "P318":
    case "P356":
    case "P369":
    case "P375":
    case "P387":
    case "P388":
    case "P398":
    case "P399":
    case "P396":
    case "P403":
    case "P404":
    case "P405":
    case "P411":
    case "P412":
    case "P417":
    case "P419":
    case "P427":
    case "P428":
    case "P420":
    case "P432":
    case "P433":
    case "P434":
    case "P435":
    case "P436":
    case "P437":
    case "P429":
    case "P449":
    case "P446":
    case "P447":
    case "P456":
    case "P448":
    case "P439":
    case "P463":
    case "P464":
    case "P469":
    case "P470":
    case "P473":
    case "P474":
    case "P471":
    case "P472":
    case "P475":
    case "P476":
    case "P478":
    case "P484":
    case "P503":
    case "P504":
    case "P515":
    case "P522":
    case "P516":
    case "P517":
    case "P520":
    case "P521":
    case "P523":
    case "P524":
    case "P542":
    case "P543":
    case "P547":
    case "P548":
    case "P553":
    case "P559":
    case "P560":
    case "P567":
    case "P570":
    case "P572":
    case "P573":
    case "P574":
    case "P565":
    case "P566":
    case "P575":
    case "P580":
    case "P581":
    case "P582":
    case "P587":
    case "P588":
    case "P589":
    case "P590":
    case "P596":
    case "P597":
    case "P598":
    case "P606":
    case "P627":
    case "P628":
    case "P635":
    case "P636":
    case "P605":
    case "P633":
    case "P634":
    case "P591":
    case "P640":
    case "P641":
    case "P642":
    case "P643":
    case "P644":
    case "P645":
    case "P658":
    case "P659":
    case "P660":
    case "P661":
    case "P662":
    case "P673":
    case "P674":
    case "P646":
    case "P647":
    case "P684":
    case "P706":
    case "P708":
    case "P709":
    case "P710":
    case "P713":
    case "P714":
    case "P720":
    case "P721":
    case "P722":
    case "P723":
    case "P724":
    case "P725":
    case "P735":
    case "P748":
    case "P749":
    case "P751":
    case "P752":
    case "P766":
    case "P767":
    case "P768":
    case "P773":
    case "P776":
    case "P796":
    case "P799":
    case "P805":
    case "P806":
    case "P808":
    case "P810":
    case "P812":
    case "P815":
    case "P821":
    case "P822":
    case "P823":
    case "P825":
    case "P827":
    case "P838":
    case "P839":
    case "P840":
    case "P842":
    case "P844":
    case "P848":
    case "P849":
    case "P852":
    case "P853":
    case "P857":
    case "P860":
    case "P861":
    case "P889":
    case "P903":
    case "P920":
    case "P921":
    case "P874":
    case "P908":
    case "P1114":
    case "P1115":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${nbDotDieuTriId}`,
        {
          lichSuKyId,
          baoCaoId,
          ...mhParams,
          ...(ma === "P565" ? { loai: 10 } : ma === "P566" ? { loai: 20 } : {}),
        }
      );
    case "P086":
    case "P1288":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${nbDotDieuTriId}`,
        {
          lichSuKyId,
          baoCaoId,
          capPhatThuoc: ma == "P1288" || ma == "P1289",
          ...mhParams,
        }
      );
    case "P890":
    case "P891":
    case "P931":
    case "P938":
    case "P925":
    case "P935":
    case "P939":
    case "P982":
    case "P980":
    case "P986":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}`,
        {
          nbDotDieuTriId,
          lichSuKyId,
          baoCaoId,
          ...mhParams,
        }
      );
    case "P231":
    case "P791":
    case "P872":
    case "P1258":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          baoCaoId,
          conThu,
          ...mhParams,
        }
      );
    case "P261":
    case "P421":
    case "P793":
    case "P818":
    case "P923":
    case "P911":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          baoCaoId,
          nbDotDieuTriId,
          thoiGianThucHien: thoiGianThucHien
            ? moment(thoiGianThucHien).format("YYYY-MM-DD HH:mm:00")
            : "",
          khoaChiDinhId,
          ...mhParams,
        }
      );
    case "P719":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}`,
        {
          lichSuKyId,
          baoCaoId,
          nbDotDieuTriId,
          thoiGianThucHien: thoiGianThucHien
            ? moment(thoiGianThucHien).format("YYYY-MM-DD HH:mm:00")
            : "",
          khoaChiDinhId,
          ...mhParams,
        }
      );
    case "P390":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${bienBanHoiChanId}`,
        { lichSuKyId, baoCaoId, ...mhParams }
      );

    case "P254":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${nbDvKhamId}`,
        { lichSuKyId, baoCaoId, ...mhParams }
      );
    case "P040":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${nbGoiDvId}`,
        { lichSuKyId, baoCaoId, ...mhParams }
      );
    case "P032":
      return combineUrlParams(
        `/headless-print-file/bang-ke/${nbDotDieuTriId}`,
        {
          ma: "P032",
          maBaoCao: "EMR_BA134",
          loai: 10,
          notPrint: true,
          khongDongTab: true,
          ...mhParams,
        }
      );
    case "P062":
    case "P554":
      return combineUrlParams(
        `/headless-print-file/bang-ke/${nbDotDieuTriId}`,
        {
          ma: ma,
          maBaoCao: "EMR_BA106",
          loai: 20,
          notPrint: true,
          khongDongTab: true,
          ...mhParams,
        }
      );
    case "P678":
      return combineUrlParams(
        `/headless-print-file/bang-ke/${nbDotDieuTriId}`,
        {
          ma: ma,
          maBaoCao: "EMR_BA381",
          loai: LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU,
          notPrint: true,
          khongDongTab: true,
          ...mhParams,
        }
      );
    case "P677":
      return combineUrlParams(
        `/headless-print-file/bang-ke/${nbDotDieuTriId}`,
        {
          ma: ma,
          maBaoCao: "EMR_BA380",
          loai: LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU_BH,
          notPrint: true,
          khongDongTab: true,
          ...mhParams,
        }
      );
    case "P107":
      return combineUrlParams(
        `/headless-print-file/bang-ke/${nbDotDieuTriId}`,
        {
          ma: "P107",
          maBaoCao: "EMR_BA222",
          loai: 30,
          notPrint: true,
          khongDongTab: true,
          ...mhParams,
        }
      );
    case "P178":
      return combineUrlParams(
        `/headless-print-file/bang-ke/${nbDotDieuTriId}`,
        {
          ma: "P178",
          maBaoCao: "EMR_BA251",
          loai: 40,
          notPrint: true,
          khongDongTab: true,
          ...mhParams,
        }
      );
    case "P541":
      return combineUrlParams(
        `/headless-print-file/bang-ke/${nbDotDieuTriId}`,
        {
          ma: "P541",
          maBaoCao: "EMR_BA323",
          loai: 50,
          notPrint: true,
          khongDongTab: true,
          ...mhParams,
        }
      );
    case "P727":
      return combineUrlParams(
        `/headless-print-file/bang-ke/${nbDotDieuTriId}`,
        {
          ma: "P727",
          maBaoCao: "EMR_BA386",
          loai: 70,
          notPrint: true,
          khongDongTab: true,
          ...mhParams,
        }
      );
    case "P746":
    case "P747":
      return combineUrlParams(
        `/headless-print-file/bang-ke/${nbDotDieuTriId}`,
        {
          ma,
          maBaoCao: "EMR_BA405",
          loai: LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU,
          notPrint: true,
          khongDongTab: true,
          ...mhParams,
        }
      );
    case "P044":
    case "P073":
    case "P088":
    case "P135":
    case "P139":
    case "P143":
    case "P144":
    case "P161":
    case "P164":
    case "P165":
    case "P167":
    case "P168":
    case "P188":
    case "P208":
    case "P207":
    case "P210":
    case "P212":
    case "P229":
    case "P230":
    case "P234":
    case "P235":
    case "P236":
    case "P237":
    case "P238":
    case "P239":
    case "P186":
    case "P242":
    case "P244":
    case "P245":
    case "P259":
    case "P268":
    case "P273":
    case "P271":
    case "P275":
    case "P276":
    case "P277":
    case "P280":
    case "P322":
    case "P323":
    case "P324":
    case "P361":
    case "P366":
    case "P373":
    case "P376":
    case "P377":
    case "P401":
    case "P407":
    case "P408":
    case "P411":
    case "P423":
    case "P416":
    case "P397":
    case "P452":
    case "P458":
    case "P453":
    case "P457":
    case "P451":
    case "P459":
    case "P460":
    case "P461":
    case "P441":
    case "P483":
    case "P479":
    case "P381":
    case "P500":
    case "P501":
    case "P502":
    case "P488":
    case "P466":
    case "P467":
    case "P513":
    case "P525":
    case "P526":
    case "P527":
    case "P528":
    case "P529":
    case "P530":
    case "P562":
    case "P569":
    case "P576":
    case "P578":
    case "P585":
    case "P584":
    case "P592":
    case "P593":
    case "P609":
    case "P599":
    case "P612":
    case "P622":
    case "P626":
    case "P638":
    case "P631":
    case "P618":
    case "P667":
    case "P670":
    case "P676":
    case "P665":
    case "P679":
    case "P695":
    case "P698":
    case "P701":
    case "P703":
    case "P705":
    case "P711":
    case "P731":
    case "P732":
    case "P738":
    case "P740":
    case "P744":
    case "P745":
    case "P754":
    case "P755":
    case "P756":
    case "P772":
    case "P782":
    case "P783":
    case "P830":
    case "P836":
    case "P863":
    case "P866":
    case "P893":
    case "P897":
    case "P899":
    case "P902":
    case "P910":
    case "P934":
    case "P1130":
    case "P945":
    case "P950":
    case "P960":
    case "P972":
    case "P977":
    case "P964":
    case "P958":
    case "P984":
    case "P955":
    case "P991":
    case "P1007":
    case "P1029":
    case "P1048":
    case "P1053":
    case "P1058":
    case "P1031":
    case "P1070":
    case "P1068":
    case "P1037":
    case "P1033":
    case "P1089":
    case "P1085":
    case "P1077":
    case "P1066":
    case "P873":
    case "P1133":
    case "P1094":
    case "P1195":
    case "P1196":
    case "P1198":
    case "P1197":
    case "P1232":
    case "P1233":
    case "P1244":
    case "P151":
    case "P11131":
    case "P11128":
    case "P11130":
    case "P11125":
    case "P967":
    case "P11133":
    case "P1291":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        { lichSuKyId, baoCaoId, ...mhParams }
      );
    case "P233":
    case "P1289":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        { lichSuKyId, baoCaoId, capPhatThuoc: ma == "P1289", ...mhParams }
      );
    case "P505":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        { lichSuKyId, baoCaoId, dsTrangThaiHoan: [0, 10], ...mhParams }
      );
    case "P726":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        { lichSuKyId, baoCaoId, ...mhParams, capCuu: true }
      );
    case "P690":
    case "P691":
    case "P692":
    case "P693":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        { lichSuKyId, baoCaoId, ...mhParams }
      );
    case "P041":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${props.nbThongTinId}`,
        { lichSuKyId, baoCaoId, ...mhParams }
      );
    case "P087":
    case "P228":
    case "P912":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}`,
        {
          lichSuKyId,
          ...mhParams,
          chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu,
          baoCaoId,
        }
      );
    case "P487":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          ...mhParams,
          chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu,
          baoCaoId,
        }
      );
    case "P248":
    case "P689":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          nbDotDieuTriId,
          tuThoiGian: props.tuThoiGian
            ? moment(props.tuThoiGian).format("YYYY-MM-DD 00:00:00")
            : null,
          denThoiGian: props.denThoiGian
            ? moment(props.denThoiGian).format("YYYY-MM-DD 23:59:59")
            : null,
          khoaChiDinhId: khoaChiDinhId,
          baoCaoId,
          ...mhParams,
        }
      );
    case "P250":
      let tuThoiGian = thoiGianThucHien
        ? moment(thoiGianThucHien).format("YYYY-MM-DD 00:00:00")
        : null;
      let denThoiGian = thoiGianThucHien
        ? moment(thoiGianThucHien).format("YYYY-MM-DD 23:59:59")
        : null;
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          nbDotDieuTriId,
          tuThoiGian,
          denThoiGian,
          khoaChiDinhId: khoaChiDinhId,
          baoCaoId,
          ...mhParams,
        }
      );
    case "P137":
    case "P138":
    case "P290":
    case "P519":
    case "P518":
    case "P550":
    case "P551":
    case "P556":
    case "P571":
    case "P602":
    case "P604":
    case "P758":
    case "P759":
    case "P760":
    case "P763":
    case "P764":
    case "P765":
    case "P769":
    case "P770":
    case "P771":
    case "P778":
    case "P779":
    case "P780":
    case "P835":
    case "P951":
    case "P952":
    case "P1062":
    case "P1110":
    case "P1117":
    case "P1303":
    case "P913":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          nbDotDieuTriId,
          baoCaoId,
          ...mhParams,
        }
      );
    case "P976":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          nbDotDieuTriId,
          baoCaoId,
          loaiDichVu: LOAI_DICH_VU.KHAM,
          ngoaiTru: true,
          ...mhParams,
        }
      );
    case "P249":
    case "EMR_HSDD094":
    case "P514":
    case "P557":
    case "P558":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}`,
        {
          lichSuKyId,
          nbDotDieuTriId,
          ngayYLenh: thoiGianThucHien
            ? moment(thoiGianThucHien).format("YYYY-MM-DD")
            : null,
          khoaChiDinhId: soPhieu,
          ...mhParams,
        }
      );
    case "P989":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          nbDotDieuTriId,
          thoiGianThucHien: thoiGianThucHien
            ? moment(thoiGianThucHien).format("YYYY-MM-DD")
            : null,
          khoaChiDinhId: khoaChiDinhId,
          ...mhParams,
        }
      );
    case "P817":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          baoCaoId,
          nbDotDieuTriId,
          ...mhParams,
        }
      );
    case "P507":
    case "P834":
    case "P845":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}`,
        {
          lichSuKyId,
          nbDotDieuTriId,
          thoiGianThucHien: thoiGianThucHien
            ? moment(thoiGianThucHien).format("YYYY-MM-DD HH:mm:00")
            : null,
          khoaChiDinhId: khoaChiDinhId,
          chiDinhTuLoaiDichVu,
          ...mhParams,
        }
      );
    case "P108":
    case "P252":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}${
          soPhieu ? `/${soPhieu}` : ""
        }`,
        { lichSuKyId, nbDotDieuTriId, khoaChiDinhId: soPhieu, ...mhParams }
      );
    case "P753":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}`,
        { lichSuKyId, nbDotDieuTriId, khoaChiDinhId: soPhieu, ...mhParams }
      );
    case "P1237":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}`,
        { lichSuKyId, nbDotDieuTriId, khoaChiDinhId: soPhieu, ...mhParams }
      );
    case "P1238":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}`,
        { lichSuKyId, nbDotDieuTriId, khoaChiDinhId: soPhieu, ...mhParams }
      );
    case "P253":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          nbDotDieuTriId,
          khoaChiDinhId: khoaChiDinhId,
          ...mhParams,
        }
      );
    case "P266":
    case "P968":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}`,
        {
          lichSuKyId,
          nbDotDieuTriId,
          phieuXuatId: soPhieu,
          baoCaoId,
          ...mhParams,
        }
      );
    case "P282":
    case "P281":
    case "P614":
    case "P681":
    case "P682":
    case "P685":
    case "P686":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${
          soPhieu || nbDvKhamId
        }`,
        { lichSuKyId, ...mhParams }
      );
    case "P1057":
    case "P1218":
    case "P294":
      return "/chi-so-song/" + nbDotDieuTriId;
    case "P368":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          ...mhParams,
          ngoaiTru: true,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          loaiDichVu: LOAI_DICH_VU.KHAM,
        }
      );
    case "P370":
    case "P386":
    case "P438":
    case "P440":
      let _thoiGianThucHien = thoiGianThucHien
        ? moment(thoiGianThucHien).format("YYYY-MM-DD HH:mm:00")
        : null;

      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          nbDotDieuTriId,
          thoiGianThucHien: _thoiGianThucHien,
          lichSuKyId,
          baoCaoId,
          ...mhParams,
        }
      );
    case "P383":
    case "P379":
    case "P385":
    case "P380":
    case "P384":
    case "P378":
    case "P382":
    case "P366":
    case "P389":
    case "P1073":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}`,
        {
          nbDotDieuTriId,
          khoaChiDinhId,
          baoCaoId,
          ...mhParams,
        }
      );
    case "P885":
    case "P995":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          nbDotDieuTriId,
          baoCaoId,
          ...mhParams,
          khoaChiDinhId: undefined,
        }
      );
    case "P415":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}${
          soPhieu ? `/${soPhieu}` : ""
        }`,
        {
          lichSuKyId,
          baoCaoId,
          nbDotDieuTriId,
          ngayYLenh: thoiGianThucHien
            ? moment(thoiGianThucHien).format("YYYY-MM-DD")
            : null,
          khoaChiDinhId: khoaChiDinhId,
          ...mhParams,
        }
      );
    case "P427":
    case "P419":
    case "P428":
    case "P420":
    case "P425":
    case "P418":
    case "P426":
    case "P424":
    case "P509":
    case "P510":
    case "P511":
    case "P512":
    case "P909":
    case "P948":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}`,
        {
          nbDotDieuTriId,
          baoCaoId,
          ...mhParams,
        }
      );
    case "P883":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}`,
        {
          nbDotDieuTriId,
          baoCaoId,
          ...mhParams,
          khoaChiDinhId: null,
        }
      );
    case "P538":
    case "P942":
    case "P962":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          baoCaoId,
          nbDotDieuTriId,
          khoaChiDinhId: khoaChiDinhId,
          ...mhParams,
        }
      );
    case "P149":
    case "P617":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          baoCaoId,
          ...mhParams,
          tuThoiGian: props.tuThoiGian
            ? moment(props.tuThoiGian).format("YYYY-MM-DD HH:mm:ss")
            : null,
          denThoiGian: props.denThoiGian
            ? moment(props.denThoiGian).format("YYYY-MM-DD HH:mm:ss")
            : null,
        }
      );
    case "P623":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}/${soPhieu}`,
        {
          lichSuKyId,
          ...mhParams,
          ngayTheoDoi: props.ngayTheoDoi
            ? moment(props.ngayTheoDoi).format("YYYY-MM-DD")
            : null,
          khoaId: props.khoaId,
          loaiTheoDoi: props.loaiTheoDoi,
          baoCaoId,
        }
      );
    case "P950":
      return combineUrlParams(
        `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR[ma].maBaoCao}`,
        {
          lichSuKyId,
          ...mhParams,
          nbDvCdhaTdcnPtTtId: soPhieu,
        }
      );
    case "P1021":
      return combineUrlParams(`/headless-editor/bao-cao/${maBaoCao}`, {
        ...mhParams,
        lichSuKyId,
        baoCaoId,
        nbDotDieuTriId,
        khoaChiDinhId: khoaChiDinhId,
        boSung: soPhieu,
      });
    default:
      if (loaiBieuMau == LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA)
        return combineUrlParams(
          `/headless-editor/bao-cao/${maBaoCao}${soPhieu ? `/${soPhieu}` : ""}`,
          {
            lichSuKyId,
            ...mhParams,
            baoCaoId,
            nbDotDieuTriId,
          }
        );
      break;
  }
  return "";
};

export default {
  addFooterPage,
  pageType,
  generate,
  pullAllCss,
  pullFileCss,
  convert,
  stripHtml,
  getLinkHeadless,
};
